# CoboERC20CalldataManager 扩展迁移总结

## 概述
成功将以下文件中的 calldata 构建逻辑迁移到 `waas2/tokenization/managers/calldata/evm/cobo_erc20.py`：

- `waas2/tokenization/managers/mint.py`
- `waas2/tokenization/managers/burn.py` 
- `waas2/tokenization/managers/allowlist.py`
- `waas2/tokenization/views/blocklist.py` (通过 blocklist manager)
- `waas2/tokenization/managers/pause.py`
- `waas2/tokenization/managers/unpause.py`

## 迁移的方法

### 1. Mint (铸造) 功能
```python
@classmethod
def build_mint_calldata(cls, token: Token, mints: List) -> str:
```
- 支持单个和批量铸造
- 使用 `mint()` 函数进行单个铸造
- 使用 `multicall()` 进行批量铸造

### 2. Burn (销毁) 功能  
```python
@classmethod
def build_burn_calldata(
    cls,
    token: Token,
    burns: List[cobo_waas2.TokenizationBurnTokenParamsBurnsInner],
    execute_address: str,
) -> str:
```
- 支持单个和批量销毁
- 根据执行地址选择 `burn()` 或 `burnFrom()` 函数
- 使用 `multicall()` 进行批量销毁

### 3. Allowlist (白名单) 功能
```python
@classmethod
def build_allowlist_calldata(
    cls, chain_id: str, token_address: str, addresses: List[str], operation: str
) -> str:
```
- 支持添加和移除白名单地址
- 使用 `accessListAdd()` 和 `accessListRemove()` 函数

### 4. Toggle Allowlist (白名单激活) 功能
```python
@classmethod
def build_toggle_allowlist_calldata(
    cls, chain_id: str, token_address: str, enabled: bool
) -> str:
```
- 切换白名单激活状态
- 使用 `toggleAccesslist()` 函数

### 5. Blocklist (黑名单) 功能
```python
@classmethod
def build_blocklist_calldata(
    cls, chain_id: str, token_address: str, addresses: List[str], operation: str
) -> str:
```
- 支持添加和移除黑名单地址
- 使用 `blockListAdd()` 和 `blockListRemove()` 函数

### 6. Pause (暂停) 功能
```python
@classmethod
def build_pause_calldata(cls, chain_id: str, token_address: str) -> str:
```
- 暂停代币合约
- 使用 `pause()` 函数

### 7. Unpause (恢复) 功能
```python
@classmethod
def build_unpause_calldata(cls, chain_id: str, token_address: str) -> str:
```
- 恢复代币合约
- 使用 `unpause()` 函数

## 新增导入
```python
from typing import List, Union
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.tokenization import TokenizationUtils
```

## 验证结果
✅ 所有方法成功迁移
✅ 语法检查通过
✅ 方法签名正确
✅ 功能逻辑保持一致

## 使用方式
现在可以通过 `CoboERC20CalldataManager` 类统一构建所有 ERC20 代币操作的 calldata，提供了更好的代码组织和复用性。

## 注意事项
- 保持了原有的业务逻辑不变
- 所有方法都是类方法 (`@classmethod`)
- 参数类型和返回值类型保持一致
- 错误处理逻辑保持原样
