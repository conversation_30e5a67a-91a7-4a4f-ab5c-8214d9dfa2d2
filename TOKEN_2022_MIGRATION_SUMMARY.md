# Token2022InstructionManager 扩展迁移总结

## 概述
成功为 `waas2/tokenization/managers/calldata/solana/token_2022.py` 添加了类似 EVM 版本的 tokenization 功能，实现了 Solana Token 2022 程序的各种操作指令构建。代码遵循 clean code 规范，权限获取参考 EVM 实现方式。

## 迁移的方法

### 1. Mint (铸造) 功能
```python
@classmethod
def build_mint_instructions(
    cls, token: Token, mints: List, chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `mintTo` 指令
- 支持批量铸造操作
- 自动计算关联代币账户地址
- 处理小数位转换

### 2. Burn (销毁) 功能  
```python
@classmethod
def build_burn_instructions(
    cls, token: Token, burns: List[cobo_waas2.TokenizationBurnTokenParamsBurnsInner],
    execute_address: str, chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `burn` 指令
- 支持批量销毁操作
- 从指定账户销毁代币
- 处理小数位转换

### 3. Freeze Account (冻结账户) 功能 - 类似 Blocklist
```python
@classmethod
def build_freeze_account_instructions(
    cls, token: Token, addresses: List[str], chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `freezeAccount` 指令
- 冻结指定地址的代币账户
- 类似于 EVM 版本的黑名单功能
- 支持批量冻结操作

### 4. Thaw Account (解冻账户) 功能 - 类似 Allowlist
```python
@classmethod
def build_thaw_account_instructions(
    cls, token: Token, addresses: List[str], chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `thawAccount` 指令
- 解冻指定地址的代币账户
- 类似于 EVM 版本的白名单功能
- 支持批量解冻操作

### 5. Pause (暂停) 功能
```python
@classmethod
def build_pause_instructions(
    cls, token: Token, chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `pause` 指令
- 暂停整个 mint 的操作
- 需要 pause authority 权限

### 6. Unpause (恢复) 功能
```python
@classmethod
def build_unpause_instructions(
    cls, token: Token, chain_id: str
) -> List[SolContractCallInstruction]:
```
- 使用 Token 2022 的 `resume` 指令
- 需要 pause authority 权限

## 辅助方法

### 权限获取方法
```python
@classmethod
def _get_authority_by_role(cls, token: Token, role: TokenizationRole) -> str:
    """根据角色获取权限地址"""

@classmethod
def _get_mint_authority_from_token(cls, token: Token) -> str:
    """获取 mint authority 地址"""

@classmethod
def _get_freeze_authority_from_token(cls, token: Token) -> str:
    """获取 freeze authority 地址"""

@classmethod
def _get_pause_authority_from_token(cls, token: Token) -> str:
    """获取 pause authority 地址"""
```
- 从 TokenRole 表获取权限地址，参考 EVM 实现方式
- 根据 TokenizationRole 枚举查找对应权限
- 提供统一的权限获取机制

## 新增导入
```python
from spl.token.instructions import get_associated_token_address
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.dao.tokenization import TokenDao, TokenRoleDao
from waas2.tokenization.enums.tokenization import TokenizationRole
```

## 新增配置
在 `sol_explorer_client.py` 中添加了 SOLDEV_SOL 配置：
```python
"SOLDEV_SOL": SolExplorerClient(
    chain_id="SOLDEV_SOL",
    host="https://explorer.solana.com",
    api_url="https://api.devnet.solana.com",
),
```

## Solana vs EVM 的差异

| 功能 | EVM (CoboERC20) | Solana (Token 2022) |
|------|-----------------|---------------------|
| Mint | `mint()` 函数 | `mintTo` 指令 |
| Burn | `burn()`/`burnFrom()` 函数 | `burn` 指令 |
| Allowlist | `accessListAdd()`/`accessListRemove()` | `thawAccount` 指令 |
| Blocklist | `blockListAdd()`/`blockListRemove()` | `freezeAccount` 指令 |
| Pause | `pause()` 函数 | `pause` 指令 |
| Unpause | `unpause()` 函数 | `resume` 指令 |

## 技术特点

1. **账户模型**: Solana 使用账户模型，需要处理关联代币账户
2. **指令结构**: 返回 `SolContractCallInstruction` 列表而不是单个 calldata
3. **权限管理**: 通过不同的 authority 地址控制各种操作
4. **扩展支持**: 利用 Token 2022 的扩展功能实现高级特性

## 验证结果
✅ 所有方法成功实现
✅ 语法检查通过
✅ 方法签名正确
✅ 导入依赖正确

## 使用方式
现在可以通过 `Token2022InstructionManager` 类统一构建所有 Solana Token 2022 操作的指令，提供了与 EVM 版本对等的功能。

## 代码质量改进
- **Clean Code**: 移除冗余注释，简化代码结构
- **权限管理**: 参考 EVM 实现，从 TokenRole 表获取权限
- **错误处理**: 提供清晰的错误信息
- **代码复用**: 统一的权限获取机制

## 注意事项
- Solana 的账户模型与 EVM 不同，需要处理关联代币账户
- 权限地址从 TokenRole 表获取，需要正确配置角色
- 所有金额都需要根据 decimals 进行转换
- 支持 SOLDEV_SOL 开发网络
