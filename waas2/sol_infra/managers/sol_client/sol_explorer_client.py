import logging
from typing import Optional

import requests

logger = logging.getLogger("sol_infra")


class SolExplorerClient:
    def __init__(self, chain_id: str, host: str, api_url: str, timeout: float = 3):
        self.chain_id = chain_id
        self.host = host
        self.api_url = api_url
        self.timeout = timeout

    def get_account_info(self, program_id: str) -> Optional[dict]:
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [program_id, {"encoding": "jsonParsed"}],
        }
        response = requests.post(self.api_url, json=payload, timeout=self.timeout)
        if response.status_code != 200:
            logger.warning(
                f"Failed to get account info for chain_id: {self.chain_id} program_id: {program_id}"
            )
            return None
        return response.json()

    def get_minimum_balance_for_rent_exemption(self, size: int) -> Optional[int]:
        """获取租金豁免所需的最小余额"""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getMinimumBalanceForRentExemption",
            "params": [size]
        }
        try:
            response = requests.post(self.api_url, json=payload, timeout=self.timeout)
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    return result["result"]
        except Exception as e:
            logger.warning(
                f"Failed to get rent exemption for chain_id: {self.chain_id} size: {size}: {e}"
            )
        return None


sol_explorer_clients = {
    "SOL": SolExplorerClient(
        chain_id="SOL",
        host="https://explorer.solana.com",
        api_url="https://api.mainnet-beta.solana.com",
    ),
    "TSOL": SolExplorerClient(
        chain_id="TSOL",
        host="https://explorer.solana.com",
        api_url="https://api.testnet.solana.com",
    ),
    "SOLDEV_SOL": SolExplorerClient(
        chain_id="SOLDEV_SOL",
        host="https://explorer.solana.com",
        api_url="https://api.devnet.solana.com",
    ),
}
