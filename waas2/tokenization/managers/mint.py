import logging

import cobo_waas2
from django.db import transaction

from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.request_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationMintManager:
    @classmethod
    @transaction.atomic
    def mint(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationMintTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币铸造交易
        """
        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.MINT
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting mint operation for token {token.token_id}")

        calldata = TokenizationCalldataRouter.build_mint_calldata(token, params.mints)

        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 交易成功后创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Mint operation completed successfully, activity_id: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)
