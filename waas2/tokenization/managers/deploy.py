import logging
from collections import defaultdict

import cobo_waas2
from cobo_libs.utils.lock import CoboLock
from django.db import transaction

from custody.api.exception import CustodyException
from custody.custody.dao.custody_wallet import CustodyWalletDao
from custody.custody.dao.organization import OrganizationDao
from custody.custody.exceptions import CustodyApiException
from waas2.base.enums.wallet import WalletSubtype
from waas2.base.wallets import WalletUtils
from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import (
    ActivityDao,
    ActivityTransactionDao,
    TokenDao,
    TokenRoleDao,
)
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.managers.address.evm import TokenizationAddressManager
from waas2.tokenization.managers.calldata.evm.cobo_erc20 import CoboERC20CalldataManager
from waas2.tokenization.managers.calldata.solana.token_2022 import Token2022InstructionManager
from waas2.tokenization.managers.gas_sponsor import GasSponsorManager
from waas2.tokenization.models.activity import Activity, ActivityTransaction
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.request_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils
from waas2.transactions.dev.controllers.transaction import TransactionController
from waas2.transactions.mongo.enums.transaction_fields import TransactionCoboCategory

logger = logging.getLogger("waas2.tokenization")


class TokenizationDeployManager:
    @classmethod
    @transaction.atomic
    def deploy(
        cls,
        params: cobo_waas2.TokenizationIssuedTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币部署交易
        """
        token_params = params.token_params.actual_instance

        operation_type = cobo_waas2.TokenizationOperationType.ISSUE

        logger.info(f"Starting deploy operation for token {token_params.symbol}")

        # 创建 Token
        with CoboLock(
            f"TKZ_{params.chain_id}_{token_params.symbol}_DEPLOY",
            expire=5,
        ):
            token_id = cls._generate_token_id(params.chain_id, token_params.symbol)
            TokenizationUtils.validate_token_id(token_id)
            # 交易成功后创建 token 记录
            token = cls._create_token_record(
                params=params,
                org_id=org_id,
                contract_address="",
                token_id=token_id,
            )
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        try:
            # 构造部署合约的 calldata
            if token_params.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
                calldata = Token2022InstructionManager.build_deploy_calldata(
                    params.chain_id, params.source.actual_instance.address, token_params
                )
            elif token_params.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
                calldata = CoboERC20CalldataManager.build_deploy_calldata(
                    params.chain_id, params.source.actual_instance.address, token_params
                )
            else:
                raise InvalidParamException("Unsupported token standard")

            logger.info(f"Create token calldata: {calldata}")
            # 预计算合约地址
            contract_address = TokenizationAddressManager.predict_contract_address(
                chain_id=params.chain_id,
                from_address=params.source.actual_instance.address,
                calldata=calldata,
            )
            logger.info(f"Predicted contract address: {contract_address}")
            TokenDao.update_by_id(token.id, token_address=contract_address)
            # 执行合约部署
            request_id = generate_request_id(token_params.symbol, operation_type)
            deploy_tx = cls._execute_contract_deployment(
                biz_org_id=biz_org_id,
                org_id=org_id,
                tokenization_source=params.source,
                chain_id=params.chain_id,
                calldata=calldata,
                fee=params.fee,
                request_id=request_id,
                api_request_info=api_request_info,
                sign_info=sign_info,
            )
            TokenizationUtils.create_activity_transaction(
                org_id=org_id,
                activity_id=activity.uuid,
                tx=deploy_tx,
                action=activity.type,
            )
        except CustodyApiException as e:
            if e.error_code == CustodyException.ERROR_INSUFFICIENT_BALANCE:
                logger.info("Insufficient balance")
            else:
                raise e

        # gas sponsor tx
        execute_wallet = CustodyWalletDao.get_by_uuid(
            params.source.actual_instance.wallet_id
        )
        sponsor_tx = GasSponsorManager.sponsor(
            to_wallet=execute_wallet,
            address=params.source.actual_instance.address,
            fee=params.fee,
        )
        if sponsor_tx:
            logger.info(
                f"Generate gas sponsor tx success, request_id: {sponsor_tx.request_id}"
            )
            TokenizationUtils.create_activity_transaction(
                org_id=org_id,
                activity_id=activity.uuid,
                tx=sponsor_tx,
                action=GasSponsorManager.TRANSACTION_ACTION,
            )
        return str(activity.uuid)

    @classmethod
    def on_gas_sponsor_on_chained(
        cls, deploy_activity: Activity, gas_sponsor_transaction: ActivityTransaction
    ):
        """手续费垫付成功"""
        deploy_tx = ActivityTransactionDao.query(
            activity_id=gas_sponsor_transaction.activity_id,
            action=cobo_waas2.TokenizationOperationType.ISSUE.value,
        ).first()
        if deploy_tx:
            logger.info(
                "Deploy transaction already exists, transaction_id: %s",
                deploy_tx.transaction_id,
            )
            return

        activity = ActivityDao.get_by_uuid(gas_sponsor_transaction.activity_id)
        fee = cobo_waas2.TransactionRequestFee.from_dict(deploy_activity.fee)

        org = OrganizationDao.get_by_uuid(gas_sponsor_transaction.org_id)
        execute_wallet = CustodyWalletDao.get_by_uuid(activity.wallet_id)
        wallet_subtype = WalletUtils.get_wallet_subtype_by_wallet(execute_wallet)
        if wallet_subtype == WalletSubtype.OrgControlled:
            source = cobo_waas2.TokenizationMpcOperationSource(
                source_type=cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED,
                wallet_id=execute_wallet.uuid,
                address=activity.address,
            )
        elif wallet_subtype == WalletSubtype.Web3:
            source = cobo_waas2.TokenizationWeb3OperationSource(
                source_type=cobo_waas2.TokenizationOperationSourceType.WEB3,
                wallet_id=execute_wallet.uuid,
                address=activity.address,
            )
        else:
            raise ValueError(f"Unsupported wallet subtype: {wallet_subtype}")

        token = TokenDao.get_by_uuid(deploy_activity.token_uuid)
        token_extra = TokenizationExtra.model_validate(token.extra)
        permissions = token_extra.init_params.token_params.actual_instance.permissions
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=token.standard,
            symbol=token.symbol,
            name=token.name,
            decimals=token.decimals,
            token_access_activated=token_extra.token_access_activated,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=permissions.admin,
                minter=permissions.minter,
                manager=permissions.manager,
                burner=permissions.burner,
                pauser=permissions.pauser,
                salvager=permissions.salvager,
                upgrader=permissions.upgrader,
            ),
        )

        calldata = CoboERC20CalldataManager.build_deploy_calldata(
            token.chain_id, activity.address, token_params
        )
        # 预计算合约地址
        contract_address = TokenizationAddressManager.predict_contract_address(
            chain_id=token.chain_id,
            from_address=activity.address,
            calldata=calldata,
        )
        logger.info(f"Predicted contract address: {contract_address}")
        TokenDao.update_by_id(token.id, token_address=contract_address)

        operation_type = cobo_waas2.TokenizationOperationType.ISSUE
        request_id = generate_request_id(token_params.symbol, operation_type)
        existing_txs = ActivityTransactionDao.list_by_activity_id_and_action_and_status(
            activity_id=activity.uuid,
            action=cobo_waas2.TokenizationOperationType.ISSUE,
            status=ActivityTransaction.get_pending_or_success_statuses(),
        )
        if existing_txs:
            raise ValueError(f"Deployment for activity {activity.uuid} already exists")

        deploy_tx = cls._execute_contract_deployment(
            biz_org_id=org.id,
            org_id=org.uuid,
            tokenization_source=cobo_waas2.TokenizationTokenOperationSource(source),
            chain_id=token.chain_id,
            calldata=calldata,
            fee=fee,
            request_id=request_id,
            api_request_info=activity.api_request_info,
            sign_info=activity.sign_info,
        )
        TokenizationUtils.create_activity_transaction(
            org_id=org.uuid,
            activity_id=activity.uuid,
            tx=deploy_tx,
            action=activity.type,
        )
        logger.info(
            f"Deploy operation completed successfully, activity_uuid: {activity.uuid}, request_id: {deploy_tx.request_id}"
        )

    @classmethod
    def on_deploy_on_chained(cls, activity_id: str):
        """部署成功"""
        pass

    @classmethod
    def _generate_token_id(cls, chain_id: str, symbol: str):
        """部署成功"""
        symbol_count = TokenDao.count_by_chain_id_and_symbol_and_statuses(
            chain_id,
            symbol,
        )
        symbol_suffix = str(symbol_count) if symbol_count > 0 else ""
        chain_prefix = chain_id if "_" in chain_id else chain_id.split("_")[0]
        return f"{chain_prefix}_{symbol}_TKZ_{symbol_suffix}".upper()

    @classmethod
    def on_deploy_success(cls, token: Token):
        """激活代币"""
        # 更新代币状态
        TokenDao.update_by_id(
            token.id,
            status=cobo_waas2.TokenizationStatus.ACTIVE,
        )

        # 记录初始权限
        token_extra = TokenizationExtra.model_validate(token.extra)
        cls._create_permission_records(token.token_id, token_extra)

    @classmethod
    def on_deploy_failed(cls, activity_id: str):
        """部署失败"""
        activity = ActivityDao.get_by_uuid(activity_id)
        token = TokenDao.get_by_uuid(activity.token_uuid)
        TokenDao.update_by_id(
            token.id,
            status=cobo_waas2.TokenizationStatus.FAILED,
        )

    @classmethod
    def _validate_activity_id_and_token(cls, activity_id: str):
        """验证活动"""
        activity = ActivityDao.get_by_uuid(activity_id)
        if not activity:
            raise ValueError(f"Activity not found: {activity_id}")

        if activity.type != cobo_waas2.TokenizationOperationType.ISSUE:
            raise ValueError(f"Invalid activity type: {activity.type}")

        token = TokenDao.get_by_token_id_and_org_id(activity.token_id, activity.org_id)
        if not token:
            raise ValueError(f"Token not found: {activity.token_id}")

        return token

    @classmethod
    def _create_token_record(
        cls,
        params: cobo_waas2.TokenizationIssuedTokenRequest,
        org_id: str,
        contract_address: str,
        token_id: str,
    ):
        """创建代币记录"""
        token_params = params.token_params.actual_instance

        # 创建 token 记录
        token = TokenDao.create(
            token_id=token_id,
            org_id=org_id,
            chain_id=params.chain_id,
            token_address=contract_address,
            symbol=token_params.symbol,
            name=token_params.name,
            decimals=token_params.decimals,
            extra=TokenizationExtra(
                token_access_activated=getattr(
                    token_params, "token_access_activated", False
                ),
                init_params=params,
            ).model_dump(mode="json"),
        )

        return token

    @classmethod
    def _create_permission_records(cls, token_id: str, token_extra: TokenizationExtra):
        """创建权限记录"""
        if token_extra.init_params:
            permissions = (
                token_extra.init_params.token_params.actual_instance.permissions
            )

            address_roles = defaultdict(int)
            if permissions.admin:
                for address in permissions.admin:
                    address_roles[address] |= TokenizationRole.ADMIN.value
            if permissions.manager:
                for address in permissions.manager:
                    address_roles[address] |= TokenizationRole.MANAGER.value
            if permissions.minter:
                for address in permissions.minter:
                    address_roles[address] |= TokenizationRole.MINTER.value
            if permissions.burner:
                for address in permissions.burner:
                    address_roles[address] |= TokenizationRole.BURNER.value
            if permissions.pauser:
                for address in permissions.pauser:
                    address_roles[address] |= TokenizationRole.PAUSER.value
            if permissions.salvager:
                for address in permissions.salvager:
                    address_roles[address] |= TokenizationRole.SALVAGER.value
            if permissions.upgrader:
                for address in permissions.upgrader:
                    address_roles[address] |= TokenizationRole.UPGRADER.value

            for address, roles in address_roles.items():
                TokenRoleDao.create(
                    token_id=token_id,
                    address=address,
                    roles=roles,
                )

    @classmethod
    def _execute_contract_deployment(
        cls,
        biz_org_id: int,
        org_id: str,
        tokenization_source: cobo_waas2.TokenizationTokenOperationSource,
        chain_id: str,
        calldata: str,
        fee: cobo_waas2.TransactionRequestFee,
        request_id: str,
        api_request_info: dict,
        sign_info: dict,
    ) -> cobo_waas2.CreateTransferTransaction201Response:
        """执行合约部署"""
        # 构造合约部署目标（这里需要指向工厂合约）
        factory_address = CoboERC20ContractHelper.get_factory_address(chain_id)

        destination = cobo_waas2.ContractCallDestination(
            cobo_waas2.EvmContractCallDestination(
                destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                address=factory_address,
                calldata=calldata,
            )
        )

        source_data = tokenization_source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise ValueError(f"Unsupported source type {source_data.source_type}")

        # 构造合约调用请求
        contract_call_param = cobo_waas2.ContractCallParams(
            request_id=request_id,
            chain_id=chain_id,
            source=cobo_waas2.ContractCallSource(source),
            destination=destination,
            fee=fee,
            auto_fuel=cobo_waas2.AutoFuelType.USEPORTALPREFERENCE,
        )
        tx = TransactionController.contract_call_for_dev_api(
            param=contract_call_param,
            biz_org_id=biz_org_id,
            org_id=org_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
            cobo_category=[TransactionCoboCategory.TOKENIZATION],
        )
        return tx
