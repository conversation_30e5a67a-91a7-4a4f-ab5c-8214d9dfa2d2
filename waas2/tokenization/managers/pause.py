import logging

import cobo_waas2
from django.db import transaction

from waas2.tokenization.dao.tokenization import ActivityDao, TokenDao
from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.request_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationPauseManager:
    @classmethod
    @transaction.atomic
    def pause(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationPauseTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币暂停交易
        """

        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.PAUSE
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting pause operation for token {token.token_id}")

        calldata = TokenizationCalldataRouter.build_pause_calldata(token)

        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 交易成功后创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Pause operation completed successfully, activity_uuid: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def on_pause_success(cls, activity_id: str):
        activity = ActivityDao.get_by_uuid(str(activity_id))
        token = TokenDao.get_by_token_id_and_org_id(
            str(activity.token_id), str(activity.org_id)
        )
        TokenDao.update_by_id(
            int(str(token.id)),
            status=cobo_waas2.TokenizationStatus.PAUSING,
        )

    @classmethod
    def on_pause_failed(cls, activity_id: str):
        pass
