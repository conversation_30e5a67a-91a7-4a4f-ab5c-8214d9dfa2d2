import logging

import cobo_waas2
from django.db import transaction

from waas2.tokenization.dao.tokenization import ActivityDao, TokenDao
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.request_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationPauseManager:
    @classmethod
    @transaction.atomic
    def pause(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationPauseTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币暂停交易
        """
        # # 检查是否有冲突的正在处理中的活动
        # cls._check_conflicting_activities(token.token_id, org_id)

        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.PAUSE
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting pause operation for token {token.token_id}")

        calldata = cls._build_pause_calldata(token.chain_id, token.token_address)

        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 交易成功后创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Pause operation completed successfully, activity_uuid: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def _build_pause_calldata(cls, chain_id: str, token_address: str) -> str:
        """构造暂停合约调用数据"""
        contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
        return contract.functions.pause()._encode_transaction_data()

    @classmethod
    def on_pause_success(cls, activity_id: str):
        activity = ActivityDao.get_by_uuid(str(activity_id))
        token = TokenDao.get_by_token_id_and_org_id(
            str(activity.token_id), str(activity.org_id)
        )
        TokenDao.update_by_id(
            int(str(token.id)),
            status=cobo_waas2.TokenizationStatus.PAUSING,
        )

    @classmethod
    def on_pause_failed(cls, activity_id: str):
        pass

    @classmethod
    def _check_conflicting_activities(cls, token_id: str, org_id: str):
        """检查是否有冲突的正在处理中的暂停相关活动"""
        from waas2.developers.exceptions import InvalidParamException
        from waas2.tokenization.dao.tokenization import ActivityDao

        conflicting_operations = [
            cobo_waas2.TokenizationOperationType.PAUSE.value,
            cobo_waas2.TokenizationOperationType.UNPAUSE.value,
            cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
        ]

        processing_activities = ActivityDao.query_activities(
            org_id=org_id,
            token_id=token_id,
            activity_status=cobo_waas2.TokenizationActivityStatus.PROCESSING.value,
        ).filter(type__in=conflicting_operations)

        if processing_activities.exists():
            conflicting_activity = processing_activities.first()
            raise InvalidParamException(
                f"Cannot execute pause operation: "
                f"There is already a processing {conflicting_activity.type} activity "
                f"(ID: {conflicting_activity.uuid}) for this token. "
                f"Please wait for the previous operation to complete."
            )
