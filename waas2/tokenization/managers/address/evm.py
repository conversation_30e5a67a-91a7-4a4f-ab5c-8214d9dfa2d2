from web3 import Web3

from waas2.tokenization.utils.contract import CoboERC20ContractHelper


class TokenizationAddressManager(object):
    @classmethod
    def predict_contract_address(
        cls, chain_id: str, from_address: str, calldata: str
    ) -> str:
        """根据 calldata 预估合约地址"""
        factory_contract = CoboERC20ContractHelper.get_factory_contract(chain_id)
        w3 = factory_contract.w3

        # 使用 eth_call 模拟交易
        raw_result = w3.eth.call(
            {
                "from": Web3.to_checksum_address(from_address),
                "to": Web3.to_checksum_address(factory_contract.address),
                "data": calldata,
            }
        )
        output_types = ["address"]
        decoded_result = w3.codec.decode(output_types, raw_result)
        return decoded_result[0]
