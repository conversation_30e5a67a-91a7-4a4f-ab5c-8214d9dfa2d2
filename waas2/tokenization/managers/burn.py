import logging
from typing import List

import cobo_waas2
from django.db import transaction
from web3 import Web3

from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.request_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationBurnManager:
    @classmethod
    @transaction.atomic
    def burn(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationBurnTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币销毁交易
        """
        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.BURN
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting burn operation for token {token.token_id}")

        calldata = cls._build_burn_calldata(
            token, params.burns, params.source.actual_instance.address
        )

        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 交易成功后创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Burn operation completed successfully, activity_uuid: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def _build_burn_calldata(
        cls,
        token: Token,
        burns: List[cobo_waas2.TokenizationBurnTokenParamsBurnsInner],
        execute_address: str,
    ) -> str:
        """构造销毁合约调用数据"""
        contract = CoboERC20ContractHelper.get_contract(
            token.chain_id, token.token_address
        )

        if len(burns) == 1:
            # 单个销毁 - 使用 burnFrom 函数
            burn = burns[0]
            if burn.from_address.lower() == execute_address.lower():
                return contract.functions.burn(
                    TokenizationUtils.convert_abs_amount(burn.amount, token.decimals),
                )._encode_transaction_data()
            else:
                return contract.functions.burnFrom(
                    Web3.to_checksum_address(burn.from_address),
                    TokenizationUtils.convert_abs_amount(burn.amount, token.decimals),
                )._encode_transaction_data()
        else:
            # 批量销毁，使用合约自带的 multicall 功能
            burn_calls = []
            for burn in burns:
                if burn.from_address.lower() == execute_address.lower():
                    burn_calldata = contract.functions.burn(
                        TokenizationUtils.convert_abs_amount(
                            burn.amount, token.decimals
                        ),
                    )._encode_transaction_data()
                else:
                    burn_calldata = contract.functions.burnFrom(
                        Web3.to_checksum_address(burn.from_address),
                        TokenizationUtils.convert_abs_amount(
                            burn.amount, token.decimals
                        ),
                    )._encode_transaction_data()
                burn_calls.append(burn_calldata)

            return contract.functions.multicall(burn_calls)._encode_transaction_data()
