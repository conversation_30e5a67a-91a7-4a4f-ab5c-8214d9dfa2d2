import logging
from typing import List, Union
import base64

import cobo_waas2
from solders.pubkey import Pubkey
from solders.system_program import CreateAccountParams, create_account
from spl.token.instructions import get_associated_token_address
from anchorpy import Program, Provider, Wallet

from waas2.tokenization.dao.tokenization import Token<PERSON><PERSON>, TokenRoleDao
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.idls.Token2022 import TOKEN_2022_IDL
from waas2.transactions.dev.bo.transaction_query.destination import (
    SolContractCallInstruction,
    SolContractCallAccount,
)
from waas2.sol_infra.managers.sol_client.sol_explorer_client import sol_explorer_clients

logger = logging.getLogger("waas2.tokenization")

# Token 2022 Program ID (as string)
TOKEN_2022_PROGRAM_ID = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"

# System Program ID (as string)
SYSTEM_PROGRAM_ID = "********************************"

# Rent Sysvar ID (as string)
RENT_SYSVAR_ID = "SysvarRent********************************1"

# Token 2022 扩展大小常量
TOKEN_2022_BASE_MINT_SIZE = 82
TOKEN_2022_PADDING_SIZE = 83
TOKEN_2022_ACCOUNT_TYPE_SIZE = 1

# 各扩展的大小（字节）
EXTENSION_SIZES = {
    "metadata_pointer": 40,
    "permanent_delegate": 40,
    "default_account_state": 10,
    "pausable_config": 40,
    "mint_close_authority": 40,
    "transfer_hook": 40,
    "token_metadata_base": 100,  # 基础元数据大小
}


class Token2022InstructionManager(object):
    @classmethod
    def _calculate_mint_size(cls, token_params: Union[cobo_waas2.TokenizationERC20TokenParams]) -> int:
        """动态计算 Token 2022 mint 账户所需的空间"""
        # 基础大小
        total_size = TOKEN_2022_BASE_MINT_SIZE + TOKEN_2022_PADDING_SIZE + TOKEN_2022_ACCOUNT_TYPE_SIZE

        # 添加各扩展的大小
        total_size += EXTENSION_SIZES["metadata_pointer"]
        total_size += EXTENSION_SIZES["permanent_delegate"]
        total_size += EXTENSION_SIZES["default_account_state"]
        total_size += EXTENSION_SIZES["pausable_config"]
        total_size += EXTENSION_SIZES["mint_close_authority"]
        total_size += EXTENSION_SIZES["transfer_hook"]

        # Token metadata 大小取决于 name 和 symbol 的长度
        metadata_size = EXTENSION_SIZES["token_metadata_base"]
        if token_params.name:
            metadata_size += len(token_params.name.encode('utf-8'))
        if token_params.symbol:
            metadata_size += len(token_params.symbol.encode('utf-8'))

        total_size += metadata_size

        # 添加一些缓冲空间以防万一
        total_size += 50

        return total_size
    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams],
    ) -> List[SolContractCallInstruction]:
        """构造 Token 2022 部署指令

        使用 anchorpy + IDL 的方式构造指令，使用 PDA 地址：
        1. 生成 mint PDA 地址
        2. 创建 mint 账户 (System Program)
        3. 初始化 mint (Token 2022 Program)
        4. 初始化 Token 2022 扩展
        """
        # 转换执行地址（payer）
        payer_pubkey = Pubkey.from_string(execute_address)

        # 生成 mint PDA 地址
        mint_pubkey, _ = cls._generate_mint_pda(token_params, execute_address)

        instructions = []

        # 1. 创建 mint 账户 (使用 System Program)
        create_account_instruction = cls._build_create_mint_account_instruction_with_anchorpy(
            mint_pubkey, payer_pubkey, chain_id, token_params
        )
        instructions.append(create_account_instruction)

        # 2. 初始化 Token 2022 扩展
        extension_instructions = cls._build_token_2022_extension_instructions(
            mint_pubkey, execute_address, token_params, chain_id
        )
        instructions.extend(extension_instructions)

        # 3. 初始化 mint (使用 anchorpy)
        initialize_mint_instruction = cls._build_initialize_mint_instruction_with_anchorpy(
            mint_pubkey, execute_address, token_params, chain_id
        )
        instructions.append(initialize_mint_instruction)

        return instructions

    @classmethod
    def _get_minimum_balance_for_rent_exemption(cls, size: int, chain_id: str) -> int:
        """获取租金豁免所需的最小余额 (使用 sol_explorer_clients)"""
        try:
            # 使用 sol_explorer_clients 获取租金
            client = sol_explorer_clients.get(chain_id)
            if not client:
                raise ValueError(f"Unsupported Solana chain: {chain_id}")

            # 使用 SolExplorerClient 的方法
            rent = client.get_minimum_balance_for_rent_exemption(size)
            if rent is not None:
                return rent
        except Exception as e:
            logger.warning(f"Failed to get rent exemption from sol_explorer_clients for chain {chain_id}: {e}")

        # 如果查询失败，使用保守的固定值作为后备
        base_rent = size * 6960
        return max(base_rent, 1000000)  # 最少 0.001 SOL

    @classmethod
    def _generate_mint_pda(
        cls,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams],
        payer: str
    ) -> tuple[Pubkey, int]:
        """生成 mint PDA 地址 (使用真正的 Solana PDA 生成)"""
        # 使用确定性种子生成 PDA
        symbol_count = TokenDao.count_by_symbol(token_params.symbol)
        seed_string = f"TKZ_SOL_{token_params.symbol}_{symbol_count}"

        # 使用 solders 的 Pubkey.find_program_address 生成真正的 PDA
        seeds = [
            b"mint",
            seed_string.encode('utf-8'),
            bytes(Pubkey.from_string(payer))
        ]

        program_id = Pubkey.from_string(TOKEN_2022_PROGRAM_ID)
        mint_pda, bump = Pubkey.find_program_address(seeds, program_id)

        return mint_pda, bump

    @classmethod
    def _build_create_mint_account_instruction_with_anchorpy(
        cls, mint_pubkey: Pubkey, payer_pubkey: Pubkey, chain_id: str, token_params: Union[cobo_waas2.TokenizationERC20TokenParams]
    ) -> SolContractCallInstruction:
        """构造创建 mint 账户的指令（使用 anchorpy 和 solders）"""

        # 动态计算 mint 账户大小
        mint_size = cls._calculate_mint_size(token_params)

        # 计算租金
        lamports = cls._get_minimum_balance_for_rent_exemption(mint_size, chain_id)

        # 使用 solders 的 create_account 函数
        owner_pubkey = Pubkey.from_string(TOKEN_2022_PROGRAM_ID)

        create_account_params = CreateAccountParams(
            from_pubkey=payer_pubkey,
            to_pubkey=mint_pubkey,
            lamports=lamports,
            space=mint_size,
            owner=owner_pubkey,
        )

        # 生成 create_account 指令
        create_account_ix = create_account(create_account_params)

        # 转换为我们的格式
        accounts = []
        for account_meta in create_account_ix.accounts:
            accounts.append(SolContractCallAccount(
                pubkey=str(account_meta.pubkey),
                is_signer=account_meta.is_signer,
                is_writable=account_meta.is_writable,
            ))

        return SolContractCallInstruction(
            accounts=accounts,
            data=base64.b64encode(create_account_ix.data).decode('utf-8'),
            program_id=str(create_account_ix.program_id),
        )

    @classmethod
    def _build_initialize_mint_instruction_with_anchorpy(
        cls,
        mint_pubkey: Pubkey,
        mint_authority: str,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams],
        chain_id: str,
    ) -> SolContractCallInstruction:
        """使用 anchorpy 构造初始化 mint 的指令"""
        # 获取权限设置
        permissions = token_params.permissions or cobo_waas2.TokenizationTokenPermissionParams()
        freeze_authority = None
        if permissions.admin:
            freeze_authority = permissions.admin[0]

        # 创建 anchorpy 程序实例
        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        rpc_endpoint = client.api_url
        provider = Provider.local(rpc_endpoint, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        # 构造 initializeMint 指令的参数
        mint_authority_pubkey = Pubkey.from_string(mint_authority)
        freeze_authority_pubkey = Pubkey.from_string(freeze_authority) if freeze_authority else None

        # 使用 anchorpy 构造指令
        initialize_mint_ix = program.instruction["initialize_mint"](
            decimals=token_params.decimals,
            mint_authority=mint_authority_pubkey,
            freeze_authority=freeze_authority_pubkey,
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                    "rent": Pubkey.from_string(RENT_SYSVAR_ID),
                }
            )
        )

        # 转换为我们的格式
        accounts = []
        for account_meta in initialize_mint_ix.accounts:
            accounts.append(SolContractCallAccount(
                pubkey=str(account_meta.pubkey),
                is_signer=account_meta.is_signer,
                is_writable=account_meta.is_writable,
            ))

        return SolContractCallInstruction(
            accounts=accounts,
            data=base64.b64encode(initialize_mint_ix.data).decode('utf-8'),
            program_id=str(initialize_mint_ix.program_id),
        )

    @classmethod
    def _build_token_2022_extension_instructions(
        cls,
        mint_pubkey: Pubkey,
        mint_authority: str,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams],
        chain_id: str,
    ) -> List[SolContractCallInstruction]:
        """构造 Token 2022 扩展初始化指令"""
        instructions = []

        # 获取 RPC 端点
        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        rpc_endpoint = client.api_url
        provider = Provider.local(rpc_endpoint, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        mint_authority_pubkey = Pubkey.from_string(mint_authority)

        # 1. initializeMetadataPointer - 默认指向 token mint
        metadata_pointer_ix = program.instruction["initialize_metadata_pointer"](
            metadata_address=mint_pubkey,  # 默认指向 mint 本身
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(metadata_pointer_ix))

        # 2. initializePermanentDelegate - 需要配置 permanent delegate authority
        permanent_delegate_ix = program.instruction["initialize_permanent_delegate"](
            delegate=mint_authority_pubkey,  # 使用 mint authority 作为 permanent delegate
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(permanent_delegate_ix))

        # 3. initializeDefaultAccountState - 配置默认账户状态
        default_account_state_ix = program.instruction["initialize_default_account_state"](
            account_state=0,  # 0 = Uninitialized, 1 = Initialized, 2 = Frozen
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(default_account_state_ix))

        # 4. initializePausableConfig - 配置暂停权限
        pausable_config_ix = program.instruction["initialize_pausable_config"](
            authority=mint_authority_pubkey,  # 暂停权限
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(pausable_config_ix))

        # 5. initializeMintCloseAuthority - 配置 mint 关闭权限
        mint_close_authority_ix = program.instruction["initialize_mint_close_authority"](
            close_authority=mint_authority_pubkey,  # mint 关闭权限
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(mint_close_authority_ix))

        # 6. initializeTransferHook - 默认指向 null
        transfer_hook_ix = program.instruction["initialize_transfer_hook"](
            authority=None,  # 不配置 transfer hook
            transfer_hook_program_id=None,  # 不配置 transfer hook program
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(transfer_hook_ix))

        # 7. initializeTokenMetadata - 初始化代币元数据
        token_metadata_ix = program.instruction["initialize_token_metadata"](
            name=token_params.name,
            symbol=token_params.symbol,
            uri="",  # 可以后续更新
            ctx=program.ctx(
                accounts={
                    "metadata": mint_pubkey,  # metadata 账户就是 mint 账户
                    "update_authority": mint_authority_pubkey,
                    "mint": mint_pubkey,
                    "mint_authority": mint_authority_pubkey,
                }
            )
        )
        instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(token_metadata_ix))

        return instructions

    @classmethod
    def _convert_anchorpy_instruction_to_sol_contract_call(
        cls, anchorpy_instruction
    ) -> SolContractCallInstruction:
        """将 anchorpy 指令转换为 SolContractCallInstruction"""
        accounts = []
        for account_meta in anchorpy_instruction.accounts:
            accounts.append(SolContractCallAccount(
                pubkey=str(account_meta.pubkey),
                is_signer=account_meta.is_signer,
                is_writable=account_meta.is_writable,
            ))

        return SolContractCallInstruction(
            accounts=accounts,
            data=base64.b64encode(anchorpy_instruction.data).decode('utf-8'),
            program_id=str(anchorpy_instruction.program_id),
        )

    @classmethod
    def build_mint_instructions(
        cls, token: Token, mints: List, chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造铸造指令"""
        instructions = []
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        mint_authority = cls._get_mint_authority_from_token(token)
        mint_authority_pubkey = Pubkey.from_string(mint_authority)

        for mint_item in mints:
            to_address = Pubkey.from_string(mint_item.to_address)
            associated_token_account = get_associated_token_address(to_address, mint_pubkey)

            mint_to_ix = program.instruction["mintTo"](
                amount=int(mint_item.amount * (10 ** token.decimals)),
                ctx=program.ctx(
                    accounts={
                        "mint": mint_pubkey,
                        "to": associated_token_account,
                        "authority": mint_authority_pubkey,
                    }
                )
            )

            instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(mint_to_ix))

        return instructions

    @classmethod
    def build_burn_instructions(
        cls, token: Token, burns: List[cobo_waas2.TokenizationBurnTokenParamsBurnsInner],
        execute_address: str, chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造销毁指令"""
        instructions = []
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        execute_pubkey = Pubkey.from_string(execute_address)

        for burn_item in burns:
            from_address = Pubkey.from_string(burn_item.from_address)
            associated_token_account = get_associated_token_address(from_address, mint_pubkey)

            burn_ix = program.instruction["burn"](
                amount=int(burn_item.amount * (10 ** token.decimals)),
                ctx=program.ctx(
                    accounts={
                        "account": associated_token_account,
                        "mint": mint_pubkey,
                        "authority": execute_pubkey,
                    }
                )
            )

            instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(burn_ix))

        return instructions

    @classmethod
    def build_freeze_account_instructions(
        cls, token: Token, addresses: List[str], chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造冻结账户指令"""
        instructions = []
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        freeze_authority = cls._get_freeze_authority_from_token(token)
        freeze_authority_pubkey = Pubkey.from_string(freeze_authority)

        for address in addresses:
            target_address = Pubkey.from_string(address)
            associated_token_account = get_associated_token_address(target_address, mint_pubkey)

            freeze_ix = program.instruction["freezeAccount"](
                ctx=program.ctx(
                    accounts={
                        "account": associated_token_account,
                        "mint": mint_pubkey,
                        "authority": freeze_authority_pubkey,
                    }
                )
            )

            instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(freeze_ix))

        return instructions

    @classmethod
    def build_thaw_account_instructions(
        cls, token: Token, addresses: List[str], chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造解冻账户指令"""
        instructions = []
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        freeze_authority = cls._get_freeze_authority_from_token(token)
        freeze_authority_pubkey = Pubkey.from_string(freeze_authority)

        for address in addresses:
            target_address = Pubkey.from_string(address)
            associated_token_account = get_associated_token_address(target_address, mint_pubkey)

            thaw_ix = program.instruction["thawAccount"](
                ctx=program.ctx(
                    accounts={
                        "account": associated_token_account,
                        "mint": mint_pubkey,
                        "authority": freeze_authority_pubkey,
                    }
                )
            )

            instructions.append(cls._convert_anchorpy_instruction_to_sol_contract_call(thaw_ix))

        return instructions

    @classmethod
    def build_pause_instructions(
        cls, token: Token, chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造暂停指令"""
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        pause_authority = cls._get_pause_authority_from_token(token)
        pause_authority_pubkey = Pubkey.from_string(pause_authority)

        pause_ix = program.instruction["pause"](
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                    "authority": pause_authority_pubkey,
                }
            )
        )

        return [cls._convert_anchorpy_instruction_to_sol_contract_call(pause_ix)]

    @classmethod
    def build_unpause_instructions(
        cls, token: Token, chain_id: str
    ) -> List[SolContractCallInstruction]:
        """构造恢复指令"""
        mint_pubkey = Pubkey.from_string(token.token_address)

        client = sol_explorer_clients.get(chain_id)
        if not client:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")

        provider = Provider.local(client.api_url, Wallet.dummy())
        program = Program(TOKEN_2022_IDL, Pubkey.from_string(TOKEN_2022_PROGRAM_ID), provider)

        pause_authority = cls._get_pause_authority_from_token(token)
        pause_authority_pubkey = Pubkey.from_string(pause_authority)

        resume_ix = program.instruction["resume"](
            ctx=program.ctx(
                accounts={
                    "mint": mint_pubkey,
                    "authority": pause_authority_pubkey,
                }
            )
        )

        return [cls._convert_anchorpy_instruction_to_sol_contract_call(resume_ix)]

    @classmethod
    def _get_mint_authority_from_token(cls, token: Token) -> str:
        """获取 mint authority 地址"""
        return cls._get_authority_by_role(token, TokenizationRole.MINTER)

    @classmethod
    def _get_freeze_authority_from_token(cls, token: Token) -> str:
        """获取 freeze authority 地址"""
        return cls._get_authority_by_role(token, TokenizationRole.ADMIN)

    @classmethod
    def _get_pause_authority_from_token(cls, token: Token) -> str:
        """获取 pause authority 地址"""
        return cls._get_authority_by_role(token, TokenizationRole.PAUSER)

    @classmethod
    def _get_authority_by_role(cls, token: Token, role: TokenizationRole) -> str:
        """根据角色获取权限地址"""
        token_roles = TokenRoleDao.list_by_token(token.token_id)
        for token_role in token_roles:
            if TokenizationRole.has_role(token_role.roles, role):
                return token_role.address
        raise ValueError(f"No {role.name} authority found for token {token.token_id}")