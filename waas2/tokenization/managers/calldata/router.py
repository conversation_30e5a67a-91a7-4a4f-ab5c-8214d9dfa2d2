import logging
from typing import List, Union

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.managers.calldata.evm.cobo_erc20 import CoboERC20CalldataManager
from waas2.tokenization.managers.calldata.solana.token_2022 import (
    Token2022InstructionManager,
)
from waas2.tokenization.models.tokenization import Token
from waas2.transactions.dev.bo.transaction_query.destination import (
    SolContractCallInstruction,
)

logger = logging.getLogger("waas2.tokenization")


class TokenizationCalldataRouter:
    """Tokenization calldata 路由管理器，根据代币标准选择对应的实现"""

    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        params: cobo_waas2.TokenizationIssuedTokenRequest,
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造铸造 calldata/instructions"""
        token_params = params.token_params.actual_instance
        if token_params.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            return Token2022InstructionManager.build_deploy_calldata(
                params.chain_id, params.source.actual_instance.address, token_params
            )
        elif token_params.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_deploy_calldata(
                params.chain_id, params.source.actual_instance.address, token_params
            )
        else:
            raise InvalidParamException(
                f"Unsupported token standard: {token_params.standard}"
            )

    @classmethod
    def build_mint_calldata(
        cls, token: Token, mints: List, chain_id: str = None
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造铸造 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_mint_calldata(token, mints)
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            return Token2022InstructionManager.build_mint_instructions(
                token, mints, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_burn_calldata(
        cls,
        token: Token,
        burns: List[cobo_waas2.TokenizationBurnTokenParamsBurnsInner],
        execute_address: str,
        chain_id: str = None,
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造销毁 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_burn_calldata(
                token, burns, execute_address
            )
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            return Token2022InstructionManager.build_burn_instructions(
                token, burns, execute_address, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_allowlist_calldata(
        cls,
        token: Token,
        addresses: List[str],
        operation: str,
        chain_id: str = None,
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造白名单 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_allowlist_calldata(
                token.chain_id, token.token_address, addresses, operation
            )
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            # Solana 使用 thaw 实现白名单功能
            return Token2022InstructionManager.build_thaw_account_instructions(
                token, addresses, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_blocklist_calldata(
        cls,
        token: Token,
        addresses: List[str],
        operation: str,
        chain_id: str = None,
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造黑名单 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_blocklist_calldata(
                token.chain_id, token.token_address, addresses, operation
            )
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            # Solana 使用 freeze 实现黑名单功能
            return Token2022InstructionManager.build_freeze_account_instructions(
                token, addresses, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_toggle_allowlist_calldata(
        cls, token: Token, enabled: bool, chain_id: str = None
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造白名单激活状态切换 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_toggle_allowlist_calldata(
                token.chain_id, token.token_address, enabled
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_pause_calldata(
        cls, token: Token, chain_id: str = None
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造暂停 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_pause_calldata(
                token.chain_id, token.token_address
            )
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            return Token2022InstructionManager.build_pause_instructions(
                token, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")

    @classmethod
    def build_unpause_calldata(
        cls, token: Token, chain_id: str = None
    ) -> Union[str, List[SolContractCallInstruction]]:
        """构造恢复 calldata/instructions"""
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            return CoboERC20CalldataManager.build_unpause_calldata(
                token.chain_id, token.token_address
            )
        elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
            return Token2022InstructionManager.build_unpause_instructions(
                token, chain_id or token.chain_id
            )
        else:
            raise InvalidParamException(f"Unsupported token standard: {token.standard}")
