import logging
from typing import Union

import cobo_waas2
from web3 import Web3

from waas2.tokenization.dao.tokenization import TokenDao
from waas2.tokenization.utils.contract import CoboERC20ContractHelper

logger = logging.getLogger("waas2.tokenization")


class CoboERC20CalldataManager(object):
    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams],
    ) -> str:
        """构造使用 cobo factory 方式的部署合约调用数据

        1. 首先调用 initialize 初始化合约
        2. 然后通过 multicall 批量设置权限
        3. 最后放弃工厂合约的管理员权限
        """
        # 生成 salt
        salt = cls._generate_deployment_salt(token_params)
        logger.info(f"Generated uint256 salt for contract: {salt}")

        factory = CoboERC20ContractHelper.get_factory_contract(chain_id)
        execute_address = Web3.to_checksum_address(execute_address)

        init_permissions = (
            token_params.permissions or cobo_waas2.TokenizationTokenPermissionParams()
        )
        # 默认初始化权限 Admin
        if not init_permissions.admin:
            init_permissions.admin = [execute_address]

        def to_checksum_address_list(addresses):
            return [Web3.to_checksum_address(addr) for addr in addresses]

        # 构造 multicall 数据数组
        multicall_data = []

        # 1. initialize 调用
        # 使用 token_params 中的 URI，如果没有则使用空字符串
        token_uri = getattr(token_params, "uri", "") or ""
        initialize_data = cls._encode_function_call(
            "initialize(string,string,string,uint8,address)",
            [
                token_params.name,
                token_params.symbol,
                token_uri,
                token_params.decimals,
                factory.address,
            ],
        )
        multicall_data.append(initialize_data)

        # 2. 获取角色常量（使用 CoboERC20 合约的角色常量）
        # 这些角色常量需要从实际的合约中获取，这里使用预定义值
        DEFAULT_ADMIN_ROLE = b"\x00" * 32
        MANAGER_ROLE = Web3.keccak(text="MANAGER_ROLE")
        MINTER_ROLE = Web3.keccak(text="MINTER_ROLE")
        BURNER_ROLE = Web3.keccak(text="BURNER_ROLE")
        PAUSER_ROLE = Web3.keccak(text="PAUSER_ROLE")
        SALVAGER_ROLE = Web3.keccak(text="SALVAGER_ROLE")
        UPGRADER_ROLE = Web3.keccak(text="UPGRADER_ROLE")

        # 3. 为每个权限角色添加 grantRole 调用
        role_mappings = [
            (DEFAULT_ADMIN_ROLE, to_checksum_address_list(init_permissions.admin)),
            (MANAGER_ROLE, to_checksum_address_list(init_permissions.manager or [])),
            (MINTER_ROLE, to_checksum_address_list(init_permissions.minter or [])),
            (BURNER_ROLE, to_checksum_address_list(init_permissions.burner or [])),
            (PAUSER_ROLE, to_checksum_address_list(init_permissions.pauser or [])),
            (SALVAGER_ROLE, to_checksum_address_list(init_permissions.salvager or [])),
            (UPGRADER_ROLE, to_checksum_address_list(init_permissions.upgrader or [])),
        ]

        for role, addresses in role_mappings:
            for address in addresses:
                grant_role_data = cls._encode_function_call(
                    "grantRole(bytes32,address)", [role, address]
                )
                multicall_data.append(grant_role_data)

        # 4. 如果启用了 allowlist，需要给工厂临时 MANAGER 角色，然后调用 toggleAccesslist
        token_access_activated = getattr(token_params, "token_access_activated", False)
        if token_access_activated:
            # 4.1 给工厂合约临时授予 MANAGER 角色，以便调用 toggleAccesslist
            grant_factory_manager_data = cls._encode_function_call(
                "grantRole(bytes32,address)", [MANAGER_ROLE, factory.address]
            )
            multicall_data.append(grant_factory_manager_data)

            # 4.2 调用 toggleAccesslist 启用 allowlist
            toggle_allowlist_data = cls._encode_function_call(
                "toggleAccesslist(bool)", [True]
            )
            multicall_data.append(toggle_allowlist_data)

        # 5. 放弃工厂合约的所有权限
        # 5.1 工厂放弃 DEFAULT_ADMIN_ROLE
        renounce_admin_role_data = cls._encode_function_call(
            "renounceRole(bytes32,address)", [DEFAULT_ADMIN_ROLE, factory.address]
        )
        multicall_data.append(renounce_admin_role_data)

        # 5.2 如果启用了 allowlist，工厂还需要放弃 MANAGER_ROLE
        if token_access_activated:
            renounce_manager_role_data = cls._encode_function_call(
                "renounceRole(bytes32,address)", [MANAGER_ROLE, factory.address]
            )
            multicall_data.append(renounce_manager_role_data)

        # CoboERC20 的创建代码
        proxy_creation_code = CoboERC20ContractHelper.INIT_CODE

        # multicall 的 calldata
        multicall_calldata = cls._encode_function_call(
            "multicall(bytes[])", [multicall_data]
        )

        # 使用新的 deployAndInit 接口 (typ=7 参考 Solidity 脚本)
        try:
            # 将 salt 转换为 bytes32
            salt_bytes32 = salt.to_bytes(32, "big")

            # 尝试使用新的 deployAndInit 接口
            calldata = factory.functions.deployAndInit(
                7,  # typ (Create2WithSenderAndEmit)
                salt_bytes32,  # salt as bytes32
                proxy_creation_code,  # initCode
                multicall_calldata,  # callData
            )._encode_transaction_data()
            logger.info("Using new deployAndInit interface with multicall")
            return calldata
        except Exception as e:
            logger.error(f"New deployAndInit interface not available: {e}")
            raise ValueError(
                f"Multicall deployment interface not supported on chain {chain_id}: {e}"
            )

    @classmethod
    def _generate_deployment_salt(
        cls, token_params: Union[cobo_waas2.TokenizationERC20TokenParams]
    ) -> int:
        """Generate uint256 salt for contract deployment from token parameters."""
        symbol_count = TokenDao.count_by_symbol(token_params.symbol)
        string_salt = f"TKZ_{token_params.symbol}_{symbol_count}"
        # Convert the string salt to a uint256 compatible integer by hashing
        hashed_salt = Web3.keccak(text=string_salt)
        int_salt = int.from_bytes(hashed_salt, "big")
        return int_salt

    @classmethod
    def _encode_function_call(cls, function_signature: str, params: list) -> bytes:
        """编码函数调用数据"""
        import re

        from eth_abi import encode

        # 计算函数选择器
        selector = Web3.keccak(text=function_signature)[:4]

        # 从函数签名中提取参数类型
        match = re.search(r"\((.*)\)", function_signature)
        if not match:
            raise ValueError(f"Invalid function signature format: {function_signature}")

        param_types_str = match.group(1)
        if param_types_str:
            param_types = [p.strip() for p in param_types_str.split(",")]
        else:
            param_types = []

        # 编码参数
        encoded_params = encode(param_types, params)

        return selector + encoded_params
