SYSTEM_PROGRAM_IDL = {
  "address": "BYFW1vhC1ohxwRbYoLbAWs86STa25i9sD5uEusVjTYNd",
  "metadata": {
    "name": "hello_anchor",
    "version": "0.1.0",
    "spec": "0.1.0",
    "description": "Created with Anchor"
  },
  "instructions": [
    {


      "name": "initialize",


      "discriminator": [175, 175, 109, 31, 13, 152, 155, 237],

      "accounts": [

        {
          "name": "new_account",
          "writable": True,
          "signer": True
        },
        {
          "name": "signer",
          "writable": True,
          "signer": True
        },
        {
          "name": "system_program",
          "address": "11111111111111111111111111111111"
        }
      ],

      "args": [

        {
          "name": "data",
          "type": "u64"
        }
      ]
    }
  ],
  "accounts": [
    {
      "name": "NewAccount",
      "discriminator": [176, 95, 4, 118, 91, 177, 125, 232]
    }
  ],
  "types": [
    {
      "name": "NewAccount",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "data",
            "type": "u64"
          }
        ]
      }
    }
  ]
}