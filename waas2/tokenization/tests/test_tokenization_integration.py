import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationIntegration(TokenizationTestBase):
    """Tokenization 集成测试"""

    def setUp(self):
        super().setUp()
        self.erc20_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            token_access_activated=True
        )
        self.token_2022 = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL",
            token_access_activated=False
        )

    def create_mock_token(self, standard=None, chain_id=None, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=chain_id or self.chain_id,
            token_address=self.address,
            standard=standard or cobo_waas2.TokenizationTokenStandard.ERC20,
            decimals=18,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    def create_mock_mint_params(self, addresses=None, amounts=None):
        """创建 mock mint 参数"""
        addresses = addresses or [self.address]
        amounts = amounts or [1000]
        
        mints = []
        for addr, amount in zip(addresses, amounts):
            mints.append(MagicMock(to_address=addr, amount=amount))
        return mints

    def create_mock_burn_params(self, addresses=None, amounts=None):
        """创建 mock burn 参数"""
        addresses = addresses or [self.address]
        amounts = amounts or [500]
        
        burns = []
        for addr, amount in zip(addresses, amounts):
            burns.append(MagicMock(from_address=addr, amount=amount))
        return burns

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_pause_calldata')
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_unpause_calldata')
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_burn_calldata')
    def test_erc20_complete_workflow(self, mock_burn, mock_unpause, mock_pause, mock_mint):
        """测试 ERC20 完整工作流程：mint → pause → unpause → burn"""
        # Setup mocks
        mock_mint.return_value = "0x123456"
        mock_pause.return_value = "0x8c379a00"
        mock_unpause.return_value = "0x3f4ba83a"
        mock_burn.return_value = "0x789abc"
        
        mints = self.create_mock_mint_params()
        burns = self.create_mock_burn_params()
        
        # Execute workflow
        mint_calldata = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
        pause_calldata = TokenizationCalldataRouter.build_pause_calldata(self.erc20_token)
        unpause_calldata = TokenizationCalldataRouter.build_unpause_calldata(self.erc20_token)
        burn_calldata = TokenizationCalldataRouter.build_burn_calldata(
            self.erc20_token, burns, self.address
        )
        
        # Verify
        self.assertEqual(mint_calldata, "0x123456")
        self.assertEqual(pause_calldata, "0x8c379a00")
        self.assertEqual(unpause_calldata, "0x3f4ba83a")
        self.assertEqual(burn_calldata, "0x789abc")
        
        mock_mint.assert_called_once_with(self.erc20_token, mints)
        mock_pause.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address
        )
        mock_unpause.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address
        )
        mock_burn.assert_called_once_with(self.erc20_token, burns, self.address)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_pause_instructions')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_unpause_instructions')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_burn_instructions')
    def test_token_2022_complete_workflow(self, mock_burn, mock_unpause, mock_pause, mock_mint):
        """测试 Token 2022 完整工作流程：mint → pause → unpause → burn"""
        # Setup mocks
        mock_instructions = [MagicMock() for _ in range(4)]
        mock_mint.return_value = [mock_instructions[0]]
        mock_pause.return_value = [mock_instructions[1]]
        mock_unpause.return_value = [mock_instructions[2]]
        mock_burn.return_value = [mock_instructions[3]]
        
        mints = self.create_mock_mint_params()
        burns = self.create_mock_burn_params()
        
        # Execute workflow
        mint_instructions = TokenizationCalldataRouter.build_mint_calldata(self.token_2022, mints)
        pause_instructions = TokenizationCalldataRouter.build_pause_calldata(self.token_2022)
        unpause_instructions = TokenizationCalldataRouter.build_unpause_calldata(self.token_2022)
        burn_instructions = TokenizationCalldataRouter.build_burn_calldata(
            self.token_2022, burns, self.address
        )
        
        # Verify
        self.assertEqual(len(mint_instructions), 1)
        self.assertEqual(len(pause_instructions), 1)
        self.assertEqual(len(unpause_instructions), 1)
        self.assertEqual(len(burn_instructions), 1)
        
        mock_mint.assert_called_once_with(self.token_2022, mints, "SOLDEV_SOL")
        mock_pause.assert_called_once_with(self.token_2022, "SOLDEV_SOL")
        mock_unpause.assert_called_once_with(self.token_2022, "SOLDEV_SOL")
        mock_burn.assert_called_once_with(self.token_2022, burns, self.address, "SOLDEV_SOL")

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_allowlist_calldata')
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_blocklist_calldata')
    def test_erc20_access_control_workflow(self, mock_blocklist, mock_allowlist):
        """测试 ERC20 访问控制工作流程"""
        mock_allowlist.return_value = "0xabc123"
        mock_blocklist.return_value = "0xdef456"
        
        addresses = [self.address, "0x1234567890123456789012345678901234567890"]
        grant_operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        remove_operation = cobo_waas2.TokenizationUpdateAddressAction.REMOVE
        
        # Execute
        allowlist_grant = TokenizationCalldataRouter.build_allowlist_calldata(
            self.erc20_token, addresses, grant_operation
        )
        allowlist_remove = TokenizationCalldataRouter.build_allowlist_calldata(
            self.erc20_token, addresses, remove_operation
        )
        blocklist_grant = TokenizationCalldataRouter.build_blocklist_calldata(
            self.erc20_token, addresses, grant_operation
        )
        blocklist_remove = TokenizationCalldataRouter.build_blocklist_calldata(
            self.erc20_token, addresses, remove_operation
        )
        
        # Verify
        self.assertEqual(allowlist_grant, "0xabc123")
        self.assertEqual(allowlist_remove, "0xabc123")
        self.assertEqual(blocklist_grant, "0xdef456")
        self.assertEqual(blocklist_remove, "0xdef456")
        
        self.assertEqual(mock_allowlist.call_count, 2)
        self.assertEqual(mock_blocklist.call_count, 2)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_thaw_account_instructions')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_freeze_account_instructions')
    def test_token_2022_access_control_workflow(self, mock_freeze, mock_thaw):
        """测试 Token 2022 访问控制工作流程"""
        mock_thaw.return_value = [MagicMock()]
        mock_freeze.return_value = [MagicMock()]
        
        addresses = [self.address, "11111111111111111111111111111111"]
        grant_operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        remove_operation = cobo_waas2.TokenizationUpdateAddressAction.REMOVE
        
        # Execute
        allowlist_grant = TokenizationCalldataRouter.build_allowlist_calldata(
            self.token_2022, addresses, grant_operation
        )
        allowlist_remove = TokenizationCalldataRouter.build_allowlist_calldata(
            self.token_2022, addresses, remove_operation
        )
        blocklist_grant = TokenizationCalldataRouter.build_blocklist_calldata(
            self.token_2022, addresses, grant_operation
        )
        blocklist_remove = TokenizationCalldataRouter.build_blocklist_calldata(
            self.token_2022, addresses, remove_operation
        )
        
        # Verify
        self.assertEqual(len(allowlist_grant), 1)
        self.assertEqual(len(allowlist_remove), 1)
        self.assertEqual(len(blocklist_grant), 1)
        self.assertEqual(len(blocklist_remove), 1)
        
        self.assertEqual(mock_thaw.call_count, 2)  # allowlist 使用 thaw
        self.assertEqual(mock_freeze.call_count, 2)  # blocklist 使用 freeze

    def test_batch_operations_performance(self):
        """测试批量操作性能"""
        # 创建大量 mint 操作
        large_batch_addresses = [f"0x{i:040x}" for i in range(100)]
        large_batch_amounts = [1000 + i for i in range(100)]
        large_mints = self.create_mock_mint_params(large_batch_addresses, large_batch_amounts)
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x" + "00" * 1000  # 大的 calldata
            
            # 执行批量操作
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, large_mints)
            
            # 验证
            self.assertIsNotNone(result)
            mock_mint.assert_called_once_with(self.erc20_token, large_mints)

    def test_cross_chain_compatibility(self):
        """测试跨链兼容性"""
        # 不同链的 ERC20 代币
        eth_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            chain_id="ETH"
        )
        bsc_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            chain_id="BSC"
        )
        
        # 不同链的 Solana 代币
        sol_mainnet_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOL"
        )
        sol_devnet_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL"
        )
        
        mints = self.create_mock_mint_params()
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_erc20_mint:
            with patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions') as mock_token_2022_mint:
                mock_erc20_mint.return_value = "0x123456"
                mock_token_2022_mint.return_value = [MagicMock()]
                
                # 执行跨链操作
                eth_result = TokenizationCalldataRouter.build_mint_calldata(eth_token, mints)
                bsc_result = TokenizationCalldataRouter.build_mint_calldata(bsc_token, mints)
                sol_mainnet_result = TokenizationCalldataRouter.build_mint_calldata(sol_mainnet_token, mints)
                sol_devnet_result = TokenizationCalldataRouter.build_mint_calldata(sol_devnet_token, mints)
                
                # 验证
                self.assertEqual(eth_result, "0x123456")
                self.assertEqual(bsc_result, "0x123456")
                self.assertEqual(len(sol_mainnet_result), 1)
                self.assertEqual(len(sol_devnet_result), 1)
                
                # 验证调用参数
                self.assertEqual(mock_erc20_mint.call_count, 2)  # ETH + BSC
                self.assertEqual(mock_token_2022_mint.call_count, 2)  # SOL + SOLDEV_SOL
                
                # 验证 chain_id 传递正确
                sol_calls = mock_token_2022_mint.call_args_list
                self.assertEqual(sol_calls[0][0][2], "SOL")  # 第一次调用的 chain_id
                self.assertEqual(sol_calls[1][0][2], "SOLDEV_SOL")  # 第二次调用的 chain_id
