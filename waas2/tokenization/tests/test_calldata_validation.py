"""
Calldata 和 Instructions 验证测试
演示如何在测试中验证生成的 calldata 和 instructions 的正确性
"""

import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.tests.test_base import TokenizationTestBase
from waas2.tokenization.tests.helpers.test_helpers import (
    TokenizationTestHelper, CalldataTestMixin, ValidationTestCase
)
from waas2.tokenization.tests.validators.calldata_validator import TokenizationValidator
from waas2.transactions.dev.bo.transaction_query.destination import (
    SolContractCallAccount, SolContractCallInstruction
)


class TestCalldataValidation(TokenizationTestBase, CalldataTestMixin, ValidationTestCase):
    """Calldata 验证测试"""

    def setUp(self):
        super().setUp()
        ValidationTestCase.setUp(self)
        
        self.erc20_token = self.helper.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            chain_id="ETH",
            token_address="******************************************"
        )
        
        self.token_2022 = self.helper.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL",
            token_address="So11111111111111111111111111111111111111112",
            token_access_activated=False
        )

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    @patch('waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract')
    def test_erc20_mint_calldata_validation(self, mock_get_contract, mock_build_mint):
        """测试 ERC20 mint calldata 验证"""
        # Setup mocks
        mock_calldata = "0x40c10f19000000000000000000000000123456789012345678901234567890123456789000000000000000000000000000000000000000000000000de0b6b3a7640000"
        mock_build_mint.return_value = mock_calldata
        
        # Mock contract for validation
        mock_contract = self.mock_contract_helper.create_mock_erc20_contract()
        mock_get_contract.return_value = mock_contract
        
        # Create test data
        mints = self.helper.create_mock_mint_params(
            addresses=["******************************************"],
            amounts=[1.0]
        )
        
        # Execute
        result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
        
        # Validate using our validator
        validation_params = self.create_erc20_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "******************************************", "amount": 1.0}]
        )
        
        self.assert_calldata_valid(
            result, "mint", "ERC20", validation_params,
            "ERC20 mint calldata should be valid"
        )
        
        # Get detailed validation results
        details = self.get_validation_details(result, "mint", "ERC20", validation_params)
        self.assertEqual(details["function_name"], "mint")
        self.assertIn("recipients", details["decoded_params"])
        self.assertIn("amounts", details["decoded_params"])

    def test_token_2022_mint_instructions_validation_with_access_control(self):
        """测试 Token 2022 mint instructions 验证（包含访问控制）"""
        # Create mock instructions for mint + freeze
        mint_instruction = SolContractCallInstruction(
            accounts=[
                SolContractCallAccount(
                    pubkey="So11111111111111111111111111111111111111112",  # mint
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="11111111111111111111111111111111",  # to account
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="22222222222222222222222222222222",  # authority
                    is_signer=True,
                    is_writable=False
                )
            ],
            data="bWludFRvRGF0YQ==",  # base64 encoded mint data
            program_id="TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
        )
        
        freeze_instruction = SolContractCallInstruction(
            accounts=[
                SolContractCallAccount(
                    pubkey="11111111111111111111111111111111",  # account to freeze
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="So11111111111111111111111111111111111111112",  # mint
                    is_signer=False,
                    is_writable=False
                ),
                SolContractCallAccount(
                    pubkey="33333333333333333333333333333333",  # freeze authority
                    is_signer=True,
                    is_writable=False
                )
            ],
            data="ZnJlZXplRGF0YQ==",  # base64 encoded freeze data
            program_id="TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
        )
        
        instructions = [mint_instruction, freeze_instruction]
        
        # Validate with access control disabled (should have freeze instruction)
        validation_params = self.create_token_2022_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "11111111111111111111111111111111", "amount": 1.0}],
            token_access_activated=False
        )
        
        self.assert_calldata_valid(
            instructions, "mint", "SPLTOKEN2022", validation_params,
            "Token 2022 mint instructions with freeze should be valid"
        )
        
        # Get detailed validation results
        details = self.get_validation_details(instructions, "mint", "SPLTOKEN2022", validation_params)
        self.assertEqual(details["instruction_count"], 2)
        self.assertEqual(details["expected_count"], 2)  # 1 mint + 1 freeze

    def test_token_2022_mint_instructions_validation_without_access_control(self):
        """测试 Token 2022 mint instructions 验证（无访问控制）"""
        # Create mock instruction for mint only
        mint_instruction = SolContractCallInstruction(
            accounts=[
                SolContractCallAccount(
                    pubkey="So11111111111111111111111111111111111111112",
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="11111111111111111111111111111111",
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="22222222222222222222222222222222",
                    is_signer=True,
                    is_writable=False
                )
            ],
            data="bWludFRvRGF0YQ==",
            program_id="TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
        )
        
        instructions = [mint_instruction]
        
        # Validate with access control enabled (should not have freeze instruction)
        validation_params = self.create_token_2022_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "11111111111111111111111111111111", "amount": 1.0}],
            token_access_activated=True
        )
        
        self.assert_calldata_valid(
            instructions, "mint", "SPLTOKEN2022", validation_params,
            "Token 2022 mint instructions without freeze should be valid"
        )

    def test_invalid_calldata_validation(self):
        """测试无效 calldata 验证"""
        invalid_calldata = "0xinvalid"
        
        validation_params = self.create_erc20_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "******************************************", "amount": 1.0}]
        )
        
        self.assert_calldata_invalid(
            invalid_calldata, "mint", "ERC20", validation_params,
            expected_error="Calldata decoding failed",
            msg="Invalid calldata should fail validation"
        )

    def test_invalid_instruction_validation(self):
        """测试无效 instruction 验证"""
        # Create instruction with invalid program_id
        invalid_instruction = SolContractCallInstruction(
            accounts=[
                SolContractCallAccount(
                    pubkey="So11111111111111111111111111111111111111112",
                    is_signer=False,
                    is_writable=True
                )
            ],
            data="aW52YWxpZA==",
            program_id="InvalidProgramId1111111111111111111111111"
        )
        
        instructions = [invalid_instruction]
        
        validation_params = self.create_token_2022_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "11111111111111111111111111111111", "amount": 1.0}],
            token_access_activated=True
        )
        
        self.assert_calldata_invalid(
            instructions, "mint", "SPLTOKEN2022", validation_params,
            expected_error="Invalid program_id",
            msg="Invalid instruction should fail validation"
        )

    def test_instruction_count_mismatch_validation(self):
        """测试指令数量不匹配验证"""
        # Create only one instruction but expect two mints
        mint_instruction = self.mock_contract_helper.create_mock_sol_instruction()
        instructions = [mint_instruction]
        
        validation_params = self.create_token_2022_validation_params(
            operation_type="mint",
            expected_mints=[
                {"to_address": "11111111111111111111111111111111", "amount": 1.0},
                {"to_address": "22222222222222222222222222222222", "amount": 2.0}
            ],
            token_access_activated=True
        )
        
        self.assert_calldata_invalid(
            instructions, "mint", "SPLTOKEN2022", validation_params,
            expected_error="Expected 2 instructions, got 1",
            msg="Instruction count mismatch should fail validation"
        )

    def test_validation_with_warnings(self):
        """测试带警告的验证"""
        # Create instruction that will generate warnings
        mint_instruction = self.mock_contract_helper.create_mock_sol_instruction()
        freeze_instruction = self.mock_contract_helper.create_mock_sol_instruction()
        instructions = [mint_instruction, freeze_instruction]
        
        # Validate with access control enabled but freeze instruction present
        validation_params = self.create_token_2022_validation_params(
            operation_type="mint",
            expected_mints=[{"to_address": "11111111111111111111111111111111", "amount": 1.0}],
            token_access_activated=True  # This should generate a warning about unexpected freeze
        )
        
        details = self.get_validation_details(instructions, "mint", "SPLTOKEN2022", validation_params)
        
        # Should be valid but with warnings
        self.assertTrue(details["valid"])
        self.assertGreater(len(details.get("warnings", [])), 0)

    def test_comprehensive_validation_workflow(self):
        """测试完整的验证工作流程"""
        # Test both ERC20 and Token 2022 in one workflow
        
        # 1. ERC20 mint validation
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_erc20_mint:
            with patch('waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract') as mock_get_contract:
                mock_erc20_mint.return_value = "0x123456"
                mock_get_contract.return_value = self.mock_contract_helper.create_mock_erc20_contract()
                
                mints = self.helper.create_mock_mint_params()
                erc20_result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
                
                erc20_params = self.create_erc20_validation_params(
                    operation_type="mint",
                    expected_mints=[{"to_address": "******************************************", "amount": 1000.0}]
                )
                
                self.assert_calldata_valid(erc20_result, "mint", "ERC20", erc20_params)
        
        # 2. Token 2022 mint validation
        with patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions') as mock_token_2022_mint:
            mock_instructions = [self.mock_contract_helper.create_mock_sol_instruction()]
            mock_token_2022_mint.return_value = mock_instructions
            
            token_2022_result = TokenizationCalldataRouter.build_mint_calldata(self.token_2022, mints)
            
            token_2022_params = self.create_token_2022_validation_params(
                operation_type="mint",
                expected_mints=[{"to_address": "******************************************", "amount": 1000.0}],
                token_access_activated=False
            )
            
            # This should fail because we only have 1 instruction but expect 2 (mint + freeze)
            self.assert_calldata_invalid(
                token_2022_result, "mint", "SPLTOKEN2022", token_2022_params,
                expected_error="Expected 2 instructions, got 1"
            )
