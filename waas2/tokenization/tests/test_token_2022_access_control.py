import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2
from solders.pubkey import Pubkey

from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.solana.token_2022 import Token2022InstructionManager
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestToken2022AccessControl(TokenizationTestBase):
    """Token 2022 访问控制测试"""

    def setUp(self):
        super().setUp()
        self.chain_id = "SOLDEV_SOL"
        self.mint_address = "So11111111111111111111111111111111111111112"
        self.to_address = "11111111111111111111111111111111"
        self.mint_authority = "22222222222222222222222222222222"
        self.freeze_authority = "33333333333333333333333333333333"

    def create_mock_token(self, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=self.chain_id,
            token_address=self.mint_address,
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            decimals=9,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    def create_mock_mint_item(self, amount=1000):
        """创建 mock mint item"""
        return MagicMock(
            to_address=self.to_address,
            amount=amount
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_mint_with_access_control_activated(self, mock_build_mint):
        """测试访问控制激活时的铸造（不冻结新代币）"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=True)
        mint_item = self.create_mock_mint_item()

        # Mock 返回只有 mint 指令，没有 freeze 指令
        mock_build_mint.return_value = [MagicMock()]

        # Execute
        instructions = Token2022InstructionManager.build_mint_instructions(
            token, [mint_item], self.chain_id
        )

        # Verify
        self.assertEqual(len(instructions), 1)  # 只有 mint 指令，没有 freeze 指令
        mock_build_mint.assert_called_once_with(token, [mint_item], self.chain_id)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_mint_with_access_control_deactivated(self, mock_build_mint):
        """测试访问控制未激活时的铸造（冻结新代币）"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=False)
        mint_item = self.create_mock_mint_item()

        # Mock 返回 mint + freeze 指令
        mock_build_mint.return_value = [MagicMock(), MagicMock()]

        # Execute
        instructions = Token2022InstructionManager.build_mint_instructions(
            token, [mint_item], self.chain_id
        )

        # Verify
        self.assertEqual(len(instructions), 2)  # mint + freeze 指令
        mock_build_mint.assert_called_once_with(token, [mint_item], self.chain_id)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_batch_mint_with_access_control(self, mock_build_mint):
        """测试批量铸造的访问控制"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=False)
        mint_items = [
            self.create_mock_mint_item(1000),
            self.create_mock_mint_item(2000),
            self.create_mock_mint_item(3000)
        ]

        # Mock 返回 3 mint + 3 freeze 指令
        mock_build_mint.return_value = [MagicMock() for _ in range(6)]

        # Execute
        instructions = Token2022InstructionManager.build_mint_instructions(
            token, mint_items, self.chain_id
        )

        # Verify
        self.assertEqual(len(instructions), 6)  # 3 mint + 3 freeze 指令
        mock_build_mint.assert_called_once_with(token, mint_items, self.chain_id)

    def test_get_token_access_activation_true(self):
        """测试获取访问控制状态 - 激活"""
        token = self.create_mock_token(token_access_activated=True)
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertTrue(result)

    def test_get_token_access_activation_false(self):
        """测试获取访问控制状态 - 未激活"""
        token = self.create_mock_token(token_access_activated=False)
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertFalse(result)

    def test_get_token_access_activation_invalid_extra(self):
        """测试获取访问控制状态 - 无效的 extra 数据"""
        token = MagicMock(
            token_id=self.token_id,
            extra="invalid_json"
        )
        
        with patch('waas2.tokenization.managers.calldata.solana.token_2022.logger') as mock_logger:
            result = Token2022InstructionManager._get_token_access_activation(token)
            
            self.assertFalse(result)  # 默认返回 False
            mock_logger.warning.assert_called_once()

    def test_get_token_access_activation_missing_extra(self):
        """测试获取访问控制状态 - 缺少 extra 字段"""
        token = MagicMock(
            token_id=self.token_id,
            extra={}
        )
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertFalse(result)  # 默认返回 False

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_mint_without_freeze_authority(self, mock_build_mint):
        """测试没有 freeze authority 时的行为"""
        token = self.create_mock_token(token_access_activated=False)
        mint_item = self.create_mock_mint_item()

        # Mock 抛出异常，因为需要 freeze authority 但找不到
        mock_build_mint.side_effect = ValueError("No freeze authority found")

        # 应该抛出异常，因为需要 freeze authority 但找不到
        with self.assertRaises(ValueError):
            Token2022InstructionManager.build_mint_instructions(
                token, [mint_item], self.chain_id
            )
