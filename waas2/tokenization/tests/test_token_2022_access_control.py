import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2
from solders.pubkey import Pubkey

from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.solana.token_2022 import Token2022InstructionManager
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestToken2022AccessControl(TokenizationTestBase):
    """Token 2022 访问控制测试"""

    def setUp(self):
        super().setUp()
        self.chain_id = "SOLDEV_SOL"
        self.mint_address = "So11111111111111111111111111111111111111112"
        self.to_address = "11111111111111111111111111111111"
        self.mint_authority = "22222222222222222222222222222222"
        self.freeze_authority = "33333333333333333333333333333333"

    def create_mock_token(self, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=self.chain_id,
            token_address=self.mint_address,
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            decimals=9,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    def create_mock_mint_item(self, amount=1000):
        """创建 mock mint item"""
        return MagicMock(
            to_address=self.to_address,
            amount=amount
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_mint_authority_from_token')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_freeze_authority_from_token')
    @patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Program')
    def test_mint_with_access_control_activated(self, mock_program, mock_client_get, 
                                               mock_get_freeze_auth, mock_get_mint_auth):
        """测试访问控制激活时的铸造（不冻结新代币）"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=True)
        mint_item = self.create_mock_mint_item()
        
        mock_get_mint_auth.return_value = self.mint_authority
        mock_get_freeze_auth.return_value = self.freeze_authority
        
        mock_client = MagicMock()
        mock_client.api_url = "https://api.devnet.solana.com"
        mock_client_get.return_value = mock_client
        
        mock_program_instance = MagicMock()
        mock_program.return_value = mock_program_instance
        
        # Mock instruction creation
        mock_mint_ix = MagicMock()
        mock_program_instance.instruction = {"mintTo": MagicMock(return_value=mock_mint_ix)}
        mock_program_instance.ctx.return_value = MagicMock()
        
        # Execute
        with patch.object(Token2022InstructionManager, '_convert_anchorpy_instruction_to_sol_contract_call') as mock_convert:
            mock_convert.return_value = MagicMock()
            
            instructions = Token2022InstructionManager.build_mint_instructions(
                token, [mint_item], self.chain_id
            )
        
        # Verify
        self.assertEqual(len(instructions), 1)  # 只有 mint 指令，没有 freeze 指令
        mock_program_instance.instruction["mintTo"].assert_called_once()
        
        # 验证没有调用 freezeAccount
        self.assertNotIn("freezeAccount", mock_program_instance.instruction)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_mint_authority_from_token')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_freeze_authority_from_token')
    @patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Program')
    def test_mint_with_access_control_deactivated(self, mock_program, mock_client_get,
                                                  mock_get_freeze_auth, mock_get_mint_auth):
        """测试访问控制未激活时的铸造（冻结新代币）"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=False)
        mint_item = self.create_mock_mint_item()
        
        mock_get_mint_auth.return_value = self.mint_authority
        mock_get_freeze_auth.return_value = self.freeze_authority
        
        mock_client = MagicMock()
        mock_client.api_url = "https://api.devnet.solana.com"
        mock_client_get.return_value = mock_client
        
        mock_program_instance = MagicMock()
        mock_program.return_value = mock_program_instance
        
        # Mock instruction creation
        mock_mint_ix = MagicMock()
        mock_freeze_ix = MagicMock()
        mock_program_instance.instruction = {
            "mintTo": MagicMock(return_value=mock_mint_ix),
            "freezeAccount": MagicMock(return_value=mock_freeze_ix)
        }
        mock_program_instance.ctx.return_value = MagicMock()
        
        # Execute
        with patch.object(Token2022InstructionManager, '_convert_anchorpy_instruction_to_sol_contract_call') as mock_convert:
            mock_convert.return_value = MagicMock()
            
            instructions = Token2022InstructionManager.build_mint_instructions(
                token, [mint_item], self.chain_id
            )
        
        # Verify
        self.assertEqual(len(instructions), 2)  # mint + freeze 指令
        mock_program_instance.instruction["mintTo"].assert_called_once()
        mock_program_instance.instruction["freezeAccount"].assert_called_once()

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_mint_authority_from_token')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_freeze_authority_from_token')
    @patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Program')
    def test_batch_mint_with_access_control(self, mock_program, mock_client_get,
                                           mock_get_freeze_auth, mock_get_mint_auth):
        """测试批量铸造的访问控制"""
        # Setup mocks
        token = self.create_mock_token(token_access_activated=False)
        mint_items = [
            self.create_mock_mint_item(1000),
            self.create_mock_mint_item(2000),
            self.create_mock_mint_item(3000)
        ]
        
        mock_get_mint_auth.return_value = self.mint_authority
        mock_get_freeze_auth.return_value = self.freeze_authority
        
        mock_client = MagicMock()
        mock_client.api_url = "https://api.devnet.solana.com"
        mock_client_get.return_value = mock_client
        
        mock_program_instance = MagicMock()
        mock_program.return_value = mock_program_instance
        
        # Mock instruction creation
        mock_mint_ix = MagicMock()
        mock_freeze_ix = MagicMock()
        mock_program_instance.instruction = {
            "mintTo": MagicMock(return_value=mock_mint_ix),
            "freezeAccount": MagicMock(return_value=mock_freeze_ix)
        }
        mock_program_instance.ctx.return_value = MagicMock()
        
        # Execute
        with patch.object(Token2022InstructionManager, '_convert_anchorpy_instruction_to_sol_contract_call') as mock_convert:
            mock_convert.return_value = MagicMock()
            
            instructions = Token2022InstructionManager.build_mint_instructions(
                token, mint_items, self.chain_id
            )
        
        # Verify
        self.assertEqual(len(instructions), 6)  # 3 mint + 3 freeze 指令
        self.assertEqual(mock_program_instance.instruction["mintTo"].call_count, 3)
        self.assertEqual(mock_program_instance.instruction["freezeAccount"].call_count, 3)

    def test_get_token_access_activation_true(self):
        """测试获取访问控制状态 - 激活"""
        token = self.create_mock_token(token_access_activated=True)
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertTrue(result)

    def test_get_token_access_activation_false(self):
        """测试获取访问控制状态 - 未激活"""
        token = self.create_mock_token(token_access_activated=False)
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertFalse(result)

    def test_get_token_access_activation_invalid_extra(self):
        """测试获取访问控制状态 - 无效的 extra 数据"""
        token = MagicMock(
            token_id=self.token_id,
            extra="invalid_json"
        )
        
        with patch('waas2.tokenization.managers.calldata.solana.token_2022.logger') as mock_logger:
            result = Token2022InstructionManager._get_token_access_activation(token)
            
            self.assertFalse(result)  # 默认返回 False
            mock_logger.warning.assert_called_once()

    def test_get_token_access_activation_missing_extra(self):
        """测试获取访问控制状态 - 缺少 extra 字段"""
        token = MagicMock(
            token_id=self.token_id,
            extra={}
        )
        
        result = Token2022InstructionManager._get_token_access_activation(token)
        
        self.assertFalse(result)  # 默认返回 False

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager._get_mint_authority_from_token')
    @patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get')
    def test_mint_without_freeze_authority(self, mock_client_get, mock_get_mint_auth):
        """测试没有 freeze authority 时的行为"""
        token = self.create_mock_token(token_access_activated=False)
        mint_item = self.create_mock_mint_item()
        
        mock_get_mint_auth.return_value = self.mint_authority
        
        mock_client = MagicMock()
        mock_client.api_url = "https://api.devnet.solana.com"
        mock_client_get.return_value = mock_client
        
        # Mock freeze authority 获取失败
        with patch.object(Token2022InstructionManager, '_get_freeze_authority_from_token') as mock_get_freeze_auth:
            mock_get_freeze_auth.side_effect = ValueError("No freeze authority found")
            
            with patch('waas2.tokenization.managers.calldata.solana.token_2022.Program') as mock_program:
                mock_program_instance = MagicMock()
                mock_program.return_value = mock_program_instance
                
                mock_mint_ix = MagicMock()
                mock_program_instance.instruction = {"mintTo": MagicMock(return_value=mock_mint_ix)}
                mock_program_instance.ctx.return_value = MagicMock()
                
                with patch.object(Token2022InstructionManager, '_convert_anchorpy_instruction_to_sol_contract_call') as mock_convert:
                    mock_convert.return_value = MagicMock()
                    
                    # 应该抛出异常，因为需要 freeze authority 但找不到
                    with self.assertRaises(ValueError):
                        Token2022InstructionManager.build_mint_instructions(
                            token, [mint_item], self.chain_id
                        )
