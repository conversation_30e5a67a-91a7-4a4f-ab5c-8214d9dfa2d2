import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.managers.calldata.solana.token_2022 import Token2022InstructionManager
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationEdgeCases(TokenizationTestBase):
    """Tokenization 边界条件和错误处理测试"""

    def setUp(self):
        super().setUp()
        self.erc20_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20
        )
        self.token_2022 = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL"
        )

    def create_mock_token(self, standard=None, chain_id=None, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=chain_id or self.chain_id,
            token_address=self.address,
            standard=standard or cobo_waas2.TokenizationTokenStandard.ERC20,
            decimals=18,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    def test_unsupported_token_standard_mint(self):
        """测试不支持的代币标准 - mint"""
        unsupported_token = self.create_mock_token(standard="UNSUPPORTED_STANDARD")
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        with self.assertRaises(InvalidParamException) as context:
            TokenizationCalldataRouter.build_mint_calldata(unsupported_token, mints)
        
        self.assertIn("Unsupported token standard", str(context.exception))

    def test_unsupported_token_standard_burn(self):
        """测试不支持的代币标准 - burn"""
        unsupported_token = self.create_mock_token(standard="UNSUPPORTED_STANDARD")
        burns = [MagicMock(from_address=self.address, amount=50)]
        
        with self.assertRaises(InvalidParamException) as context:
            TokenizationCalldataRouter.build_burn_calldata(unsupported_token, burns, self.address)
        
        self.assertIn("Unsupported token standard", str(context.exception))

    def test_unsupported_token_standard_pause(self):
        """测试不支持的代币标准 - pause"""
        unsupported_token = self.create_mock_token(standard="UNSUPPORTED_STANDARD")
        
        with self.assertRaises(InvalidParamException) as context:
            TokenizationCalldataRouter.build_pause_calldata(unsupported_token)
        
        self.assertIn("Unsupported token standard", str(context.exception))

    def test_token_2022_toggle_allowlist_unsupported(self):
        """测试 Token 2022 不支持 toggle allowlist"""
        with self.assertRaises(InvalidParamException) as context:
            TokenizationCalldataRouter.build_toggle_allowlist_calldata(self.token_2022, True)
        
        self.assertIn("Unsupported token standard", str(context.exception))

    def test_empty_mint_list(self):
        """测试空的 mint 列表"""
        empty_mints = []
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x"
            
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, empty_mints)
            
            self.assertEqual(result, "0x")
            mock_mint.assert_called_once_with(self.erc20_token, empty_mints)

    def test_empty_burn_list(self):
        """测试空的 burn 列表"""
        empty_burns = []
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_burn_calldata') as mock_burn:
            mock_burn.return_value = "0x"
            
            result = TokenizationCalldataRouter.build_burn_calldata(
                self.erc20_token, empty_burns, self.address
            )
            
            self.assertEqual(result, "0x")
            mock_burn.assert_called_once_with(self.erc20_token, empty_burns, self.address)

    def test_empty_address_list_allowlist(self):
        """测试空的地址列表 - allowlist"""
        empty_addresses = []
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_allowlist_calldata') as mock_allowlist:
            mock_allowlist.return_value = "0x"
            
            result = TokenizationCalldataRouter.build_allowlist_calldata(
                self.erc20_token, empty_addresses, operation
            )
            
            self.assertEqual(result, "0x")
            mock_allowlist.assert_called_once_with(
                self.erc20_token.chain_id, self.erc20_token.token_address, empty_addresses, operation
            )

    def test_invalid_chain_id_token_2022(self):
        """测试无效的 chain_id - Token 2022"""
        invalid_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="INVALID_CHAIN"
        )
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        with patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get') as mock_get_client:
            mock_get_client.return_value = None
            
            with self.assertRaises(ValueError) as context:
                TokenizationCalldataRouter.build_mint_calldata(invalid_token, mints)
            
            self.assertIn("Unsupported Solana chain", str(context.exception))

    def test_missing_authority_token_2022(self):
        """测试缺少权限 - Token 2022"""
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        with patch('waas2.sol_infra.managers.sol_client.sol_explorer_client.sol_explorer_clients.get') as mock_get_client:
            mock_client = MagicMock()
            mock_client.api_url = "https://api.devnet.solana.com"
            mock_get_client.return_value = mock_client
            
            with patch.object(Token2022InstructionManager, '_get_mint_authority_from_token') as mock_get_auth:
                mock_get_auth.side_effect = ValueError("No MINTER authority found")
                
                with self.assertRaises(ValueError) as context:
                    TokenizationCalldataRouter.build_mint_calldata(self.token_2022, mints)
                
                self.assertIn("No MINTER authority found", str(context.exception))

    def test_invalid_token_extra_data(self):
        """测试无效的 token extra 数据"""
        invalid_token = MagicMock(
            token_id=self.token_id,
            chain_id="SOLDEV_SOL",
            token_address=self.address,
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            decimals=18,
            extra="invalid_json_string"
        )
        
        # 测试访问控制状态获取
        result = Token2022InstructionManager._get_token_access_activation(invalid_token)
        self.assertFalse(result)  # 应该返回默认值 False

    def test_none_token_extra_data(self):
        """测试 None 的 token extra 数据"""
        none_extra_token = MagicMock(
            token_id=self.token_id,
            chain_id="SOLDEV_SOL",
            token_address=self.address,
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            decimals=18,
            extra=None
        )
        
        # 测试访问控制状态获取
        result = Token2022InstructionManager._get_token_access_activation(none_extra_token)
        self.assertFalse(result)  # 应该返回默认值 False

    def test_zero_amount_mint(self):
        """测试零金额 mint"""
        zero_mints = [MagicMock(to_address=self.address, amount=0)]
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x123456"
            
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, zero_mints)
            
            self.assertEqual(result, "0x123456")
            mock_mint.assert_called_once_with(self.erc20_token, zero_mints)

    def test_negative_amount_mint(self):
        """测试负金额 mint"""
        negative_mints = [MagicMock(to_address=self.address, amount=-100)]
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x123456"
            
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, negative_mints)
            
            self.assertEqual(result, "0x123456")
            mock_mint.assert_called_once_with(self.erc20_token, negative_mints)

    def test_very_large_amount_mint(self):
        """测试非常大的金额 mint"""
        large_amount = 10**30  # 非常大的数字
        large_mints = [MagicMock(to_address=self.address, amount=large_amount)]
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x123456"
            
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, large_mints)
            
            self.assertEqual(result, "0x123456")
            mock_mint.assert_called_once_with(self.erc20_token, large_mints)

    def test_invalid_address_format(self):
        """测试无效的地址格式"""
        invalid_address_mints = [MagicMock(to_address="invalid_address", amount=100)]
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            # 假设底层会处理地址验证
            mock_mint.side_effect = ValueError("Invalid address format")
            
            with self.assertRaises(ValueError):
                TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, invalid_address_mints)

    def test_none_chain_id_parameter(self):
        """测试 None 的 chain_id 参数"""
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        with patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions') as mock_mint:
            mock_mint.return_value = [MagicMock()]
            
            # 使用 None chain_id，应该使用 token.chain_id
            result = TokenizationCalldataRouter.build_mint_calldata(
                self.token_2022, mints, chain_id=None
            )
            
            self.assertEqual(len(result), 1)
            mock_mint.assert_called_once_with(self.token_2022, mints, "SOLDEV_SOL")

    def test_concurrent_operations_simulation(self):
        """测试并发操作模拟"""
        import threading
        import time
        
        results = []
        errors = []
        
        def mint_operation():
            try:
                mints = [MagicMock(to_address=self.address, amount=100)]
                with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
                    mock_mint.return_value = "0x123456"
                    result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
                    results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程模拟并发
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=mint_operation)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        self.assertEqual(len(results), 10)
        self.assertEqual(len(errors), 0)
        self.assertTrue(all(result == "0x123456" for result in results))

    def test_memory_usage_large_batch(self):
        """测试大批量操作的内存使用"""
        # 创建大量操作
        large_batch_size = 1000
        large_mints = [
            MagicMock(to_address=f"0x{i:040x}", amount=i)
            for i in range(large_batch_size)
        ]
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x" + "00" * 10000  # 大的 calldata
            
            # 执行大批量操作
            result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, large_mints)
            
            # 验证
            self.assertIsNotNone(result)
            mock_mint.assert_called_once_with(self.erc20_token, large_mints)
            
            # 清理内存
            del large_mints
            del result

    def test_parameter_validation_edge_cases(self):
        """测试参数验证边界条件"""
        # 测试 None 参数
        with self.assertRaises(AttributeError):
            TokenizationCalldataRouter.build_mint_calldata(None, [])

        # 测试空 token 对象
        empty_token = MagicMock()
        empty_token.standard = None

        with self.assertRaises(InvalidParamException):
            TokenizationCalldataRouter.build_mint_calldata(empty_token, [])
