"""
Calldata 和 Instructions 验证器
用于在测试阶段验证生成的 calldata 和 instructions 的正确性
"""

import base64
import json
import logging
from typing import Dict, List, Union, Any, Optional

import cobo_waas2
from web3 import Web3
from solders.pubkey import Pubkey
from solders.instruction import Instruction

from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.transactions.dev.bo.transaction_query.destination import SolContractCallInstruction

logger = logging.getLogger("waas2.tokenization.tests")


class CalldataValidator:
    """EVM Calldata 验证器"""
    
    @classmethod
    def validate_mint_calldata(
        cls, 
        calldata: str, 
        token_address: str, 
        chain_id: str,
        expected_mints: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """验证 mint calldata 的正确性"""
        try:
            # 获取合约实例
            contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
            
            # 解码 calldata
            decoded = contract.decode_function_input(calldata)
            function_obj, function_params = decoded
            
            validation_result = {
                "valid": True,
                "function_name": function_obj.function_identifier,
                "decoded_params": function_params,
                "errors": [],
                "warnings": []
            }
            
            # 验证函数名
            if function_obj.function_identifier != "mint":
                validation_result["errors"].append(
                    f"Expected 'mint' function, got '{function_obj.function_identifier}'"
                )
                validation_result["valid"] = False
            
            # 验证参数
            if "recipients" in function_params and "amounts" in function_params:
                recipients = function_params["recipients"]
                amounts = function_params["amounts"]
                
                # 验证数组长度
                if len(recipients) != len(amounts):
                    validation_result["errors"].append(
                        f"Recipients and amounts length mismatch: {len(recipients)} vs {len(amounts)}"
                    )
                    validation_result["valid"] = False
                
                # 验证与期望值的匹配
                if len(expected_mints) != len(recipients):
                    validation_result["warnings"].append(
                        f"Expected {len(expected_mints)} mints, got {len(recipients)}"
                    )
                
                # 验证地址格式
                for i, recipient in enumerate(recipients):
                    if not Web3.is_address(recipient):
                        validation_result["errors"].append(
                            f"Invalid address at index {i}: {recipient}"
                        )
                        validation_result["valid"] = False
                
                # 验证金额
                for i, amount in enumerate(amounts):
                    if amount <= 0:
                        validation_result["warnings"].append(
                            f"Zero or negative amount at index {i}: {amount}"
                        )
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "function_name": None,
                "decoded_params": {},
                "errors": [f"Calldata decoding failed: {str(e)}"],
                "warnings": []
            }
    
    @classmethod
    def validate_burn_calldata(
        cls,
        calldata: str,
        token_address: str,
        chain_id: str,
        expected_burns: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """验证 burn calldata 的正确性"""
        try:
            contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
            decoded = contract.decode_function_input(calldata)
            function_obj, function_params = decoded
            
            validation_result = {
                "valid": True,
                "function_name": function_obj.function_identifier,
                "decoded_params": function_params,
                "errors": [],
                "warnings": []
            }
            
            # 验证函数名（可能是 burn 或 burnFrom）
            expected_functions = ["burn", "burnFrom"]
            if function_obj.function_identifier not in expected_functions:
                validation_result["errors"].append(
                    f"Expected {expected_functions}, got '{function_obj.function_identifier}'"
                )
                validation_result["valid"] = False
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "function_name": None,
                "decoded_params": {},
                "errors": [f"Calldata decoding failed: {str(e)}"],
                "warnings": []
            }
    
    @classmethod
    def validate_pause_calldata(cls, calldata: str, token_address: str, chain_id: str) -> Dict[str, Any]:
        """验证 pause calldata 的正确性"""
        try:
            contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
            decoded = contract.decode_function_input(calldata)
            function_obj, function_params = decoded
            
            validation_result = {
                "valid": True,
                "function_name": function_obj.function_identifier,
                "decoded_params": function_params,
                "errors": [],
                "warnings": []
            }
            
            if function_obj.function_identifier != "pause":
                validation_result["errors"].append(
                    f"Expected 'pause' function, got '{function_obj.function_identifier}'"
                )
                validation_result["valid"] = False
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "function_name": None,
                "decoded_params": {},
                "errors": [f"Calldata decoding failed: {str(e)}"],
                "warnings": []
            }
    
    @classmethod
    def validate_allowlist_calldata(
        cls,
        calldata: str,
        token_address: str,
        chain_id: str,
        expected_addresses: List[str],
        expected_operation: str
    ) -> Dict[str, Any]:
        """验证 allowlist calldata 的正确性"""
        try:
            contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
            decoded = contract.decode_function_input(calldata)
            function_obj, function_params = decoded
            
            validation_result = {
                "valid": True,
                "function_name": function_obj.function_identifier,
                "decoded_params": function_params,
                "errors": [],
                "warnings": []
            }
            
            # 验证函数名
            expected_functions = ["accessListAdd", "accessListRemove"]
            if function_obj.function_identifier not in expected_functions:
                validation_result["errors"].append(
                    f"Expected {expected_functions}, got '{function_obj.function_identifier}'"
                )
                validation_result["valid"] = False
            
            # 验证操作类型匹配
            if expected_operation == cobo_waas2.TokenizationUpdateAddressAction.GRANT:
                if function_obj.function_identifier != "accessListAdd":
                    validation_result["errors"].append(
                        f"Expected 'accessListAdd' for GRANT operation, got '{function_obj.function_identifier}'"
                    )
                    validation_result["valid"] = False
            elif expected_operation == cobo_waas2.TokenizationUpdateAddressAction.REMOVE:
                if function_obj.function_identifier != "accessListRemove":
                    validation_result["errors"].append(
                        f"Expected 'accessListRemove' for REMOVE operation, got '{function_obj.function_identifier}'"
                    )
                    validation_result["valid"] = False
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "function_name": None,
                "decoded_params": {},
                "errors": [f"Calldata decoding failed: {str(e)}"],
                "warnings": []
            }


class InstructionValidator:
    """Solana Instructions 验证器"""
    
    # Token 2022 Program ID
    TOKEN_2022_PROGRAM_ID = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
    
    @classmethod
    def validate_mint_instructions(
        cls,
        instructions: List[SolContractCallInstruction],
        token_address: str,
        expected_mints: List[Dict[str, Any]],
        token_access_activated: bool = True
    ) -> Dict[str, Any]:
        """验证 mint instructions 的正确性"""
        validation_result = {
            "valid": True,
            "instruction_count": len(instructions),
            "expected_count": len(expected_mints) * (2 if not token_access_activated else 1),
            "errors": [],
            "warnings": [],
            "instruction_details": []
        }
        
        try:
            # 验证指令数量
            expected_count = len(expected_mints) * (2 if not token_access_activated else 1)
            if len(instructions) != expected_count:
                validation_result["errors"].append(
                    f"Expected {expected_count} instructions, got {len(instructions)}"
                )
                validation_result["valid"] = False
            
            # 验证每个指令
            mint_count = 0
            freeze_count = 0
            
            for i, instruction in enumerate(instructions):
                detail = cls._validate_single_instruction(instruction, i)
                validation_result["instruction_details"].append(detail)
                
                if not detail["valid"]:
                    validation_result["valid"] = False
                    validation_result["errors"].extend(detail["errors"])
                
                # 统计指令类型
                if detail.get("instruction_type") == "mintTo":
                    mint_count += 1
                elif detail.get("instruction_type") == "freezeAccount":
                    freeze_count += 1
            
            # 验证指令类型分布
            if mint_count != len(expected_mints):
                validation_result["errors"].append(
                    f"Expected {len(expected_mints)} mint instructions, got {mint_count}"
                )
                validation_result["valid"] = False
            
            if not token_access_activated and freeze_count != len(expected_mints):
                validation_result["errors"].append(
                    f"Expected {len(expected_mints)} freeze instructions, got {freeze_count}"
                )
                validation_result["valid"] = False
            elif token_access_activated and freeze_count > 0:
                validation_result["warnings"].append(
                    f"Unexpected freeze instructions when token_access_activated=True"
                )
            
            return validation_result
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Instruction validation failed: {str(e)}")
            return validation_result
    
    @classmethod
    def _validate_single_instruction(cls, instruction: SolContractCallInstruction, index: int) -> Dict[str, Any]:
        """验证单个指令"""
        detail = {
            "index": index,
            "valid": True,
            "errors": [],
            "warnings": [],
            "program_id": instruction.program_id,
            "account_count": len(instruction.accounts),
            "instruction_type": None
        }
        
        try:
            # 验证 program_id
            if instruction.program_id != cls.TOKEN_2022_PROGRAM_ID:
                detail["errors"].append(
                    f"Invalid program_id: expected {cls.TOKEN_2022_PROGRAM_ID}, got {instruction.program_id}"
                )
                detail["valid"] = False
            
            # 解码指令数据以确定类型
            try:
                instruction_data = base64.b64decode(instruction.data)
                # 简单的指令类型识别（基于数据长度和模式）
                if len(instruction_data) >= 8:
                    # 这里可以添加更复杂的指令解析逻辑
                    if len(instruction.accounts) == 3:  # mintTo 通常有 3 个账户
                        detail["instruction_type"] = "mintTo"
                    elif len(instruction.accounts) == 3:  # freezeAccount 也有 3 个账户
                        detail["instruction_type"] = "freezeAccount"
            except Exception:
                detail["warnings"].append("Could not decode instruction data")
            
            # 验证账户数量
            if len(instruction.accounts) < 2:
                detail["errors"].append(
                    f"Too few accounts: expected at least 2, got {len(instruction.accounts)}"
                )
                detail["valid"] = False
            
            # 验证账户格式
            for j, account in enumerate(instruction.accounts):
                try:
                    Pubkey.from_string(account.pubkey)
                except Exception:
                    detail["errors"].append(
                        f"Invalid pubkey format at account {j}: {account.pubkey}"
                    )
                    detail["valid"] = False
            
            return detail
            
        except Exception as e:
            detail["valid"] = False
            detail["errors"].append(f"Instruction validation failed: {str(e)}")
            return detail
    
    @classmethod
    def validate_burn_instructions(
        cls,
        instructions: List[SolContractCallInstruction],
        expected_burns: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """验证 burn instructions 的正确性"""
        validation_result = {
            "valid": True,
            "instruction_count": len(instructions),
            "expected_count": len(expected_burns),
            "errors": [],
            "warnings": []
        }
        
        # 基本验证
        if len(instructions) != len(expected_burns):
            validation_result["errors"].append(
                f"Expected {len(expected_burns)} instructions, got {len(instructions)}"
            )
            validation_result["valid"] = False
        
        return validation_result
    
    @classmethod
    def validate_freeze_instructions(
        cls,
        instructions: List[SolContractCallInstruction],
        expected_addresses: List[str]
    ) -> Dict[str, Any]:
        """验证 freeze instructions 的正确性"""
        validation_result = {
            "valid": True,
            "instruction_count": len(instructions),
            "expected_count": len(expected_addresses),
            "errors": [],
            "warnings": []
        }
        
        if len(instructions) != len(expected_addresses):
            validation_result["errors"].append(
                f"Expected {len(expected_addresses)} instructions, got {len(instructions)}"
            )
            validation_result["valid"] = False
        
        return validation_result


class TokenizationValidator:
    """统一的 Tokenization 验证器"""
    
    @classmethod
    def validate_calldata_or_instructions(
        cls,
        result: Union[str, List[SolContractCallInstruction]],
        operation_type: str,
        token_standard: str,
        validation_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """统一验证 calldata 或 instructions"""
        
        if isinstance(result, str):
            # EVM Calldata 验证
            return cls._validate_evm_calldata(result, operation_type, validation_params)
        elif isinstance(result, list):
            # Solana Instructions 验证
            return cls._validate_solana_instructions(result, operation_type, validation_params)
        else:
            return {
                "valid": False,
                "errors": [f"Unknown result type: {type(result)}"],
                "warnings": []
            }
    
    @classmethod
    def _validate_evm_calldata(
        cls,
        calldata: str,
        operation_type: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证 EVM calldata"""
        token_address = params.get("token_address")
        chain_id = params.get("chain_id")
        
        if operation_type == "mint":
            return CalldataValidator.validate_mint_calldata(
                calldata, token_address, chain_id, params.get("expected_mints", [])
            )
        elif operation_type == "burn":
            return CalldataValidator.validate_burn_calldata(
                calldata, token_address, chain_id, params.get("expected_burns", [])
            )
        elif operation_type == "pause":
            return CalldataValidator.validate_pause_calldata(
                calldata, token_address, chain_id
            )
        elif operation_type == "allowlist":
            return CalldataValidator.validate_allowlist_calldata(
                calldata, token_address, chain_id,
                params.get("expected_addresses", []),
                params.get("expected_operation")
            )
        else:
            return {
                "valid": False,
                "errors": [f"Unsupported operation type: {operation_type}"],
                "warnings": []
            }
    
    @classmethod
    def _validate_solana_instructions(
        cls,
        instructions: List[SolContractCallInstruction],
        operation_type: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证 Solana instructions"""
        if operation_type == "mint":
            return InstructionValidator.validate_mint_instructions(
                instructions,
                params.get("token_address"),
                params.get("expected_mints", []),
                params.get("token_access_activated", True)
            )
        elif operation_type == "burn":
            return InstructionValidator.validate_burn_instructions(
                instructions, params.get("expected_burns", [])
            )
        elif operation_type == "freeze":
            return InstructionValidator.validate_freeze_instructions(
                instructions, params.get("expected_addresses", [])
            )
        else:
            return {
                "valid": False,
                "errors": [f"Unsupported operation type: {operation_type}"],
                "warnings": []
            }
