import time
import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationPerformance(TokenizationTestBase):
    """Tokenization 性能测试"""

    def setUp(self):
        super().setUp()
        self.erc20_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20
        )
        self.token_2022 = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL"
        )

    def create_mock_token(self, standard=None, chain_id=None, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=chain_id or self.chain_id,
            token_address=self.address,
            standard=standard or cobo_waas2.TokenizationTokenStandard.ERC20,
            decimals=18,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    def create_batch_mints(self, size):
        """创建批量 mint 操作"""
        return [
            MagicMock(to_address=f"0x{i:040x}", amount=1000 + i)
            for i in range(size)
        ]

    def create_batch_burns(self, size):
        """创建批量 burn 操作"""
        return [
            MagicMock(from_address=f"0x{i:040x}", amount=500 + i)
            for i in range(size)
        ]

    def create_batch_addresses(self, size):
        """创建批量地址"""
        return [f"0x{i:040x}" for i in range(size)]

    def measure_execution_time(self, func, *args, **kwargs):
        """测量执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    def test_erc20_mint_performance_small_batch(self, mock_mint):
        """测试 ERC20 小批量 mint 性能"""
        mock_mint.return_value = "0x123456"
        small_batch = self.create_batch_mints(10)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.erc20_token, small_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 0.1)  # 应该在 100ms 内完成
        mock_mint.assert_called_once()

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    def test_erc20_mint_performance_medium_batch(self, mock_mint):
        """测试 ERC20 中等批量 mint 性能"""
        mock_mint.return_value = "0x123456"
        medium_batch = self.create_batch_mints(100)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.erc20_token, medium_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 0.5)  # 应该在 500ms 内完成
        mock_mint.assert_called_once()

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    def test_erc20_mint_performance_large_batch(self, mock_mint):
        """测试 ERC20 大批量 mint 性能"""
        mock_mint.return_value = "0x123456"
        large_batch = self.create_batch_mints(1000)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.erc20_token, large_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 2.0)  # 应该在 2s 内完成
        mock_mint.assert_called_once()

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_token_2022_mint_performance_small_batch(self, mock_mint):
        """测试 Token 2022 小批量 mint 性能"""
        mock_mint.return_value = [MagicMock() for _ in range(10)]
        small_batch = self.create_batch_mints(10)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.token_2022, small_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 0.1)  # 应该在 100ms 内完成
        mock_mint.assert_called_once()

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_token_2022_mint_performance_medium_batch(self, mock_mint):
        """测试 Token 2022 中等批量 mint 性能"""
        mock_mint.return_value = [MagicMock() for _ in range(100)]
        medium_batch = self.create_batch_mints(100)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.token_2022, medium_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 0.5)  # 应该在 500ms 内完成
        mock_mint.assert_called_once()

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_token_2022_mint_performance_large_batch(self, mock_mint):
        """测试 Token 2022 大批量 mint 性能"""
        mock_mint.return_value = [MagicMock() for _ in range(1000)]
        large_batch = self.create_batch_mints(1000)
        
        result, execution_time = self.measure_execution_time(
            TokenizationCalldataRouter.build_mint_calldata,
            self.token_2022, large_batch
        )
        
        self.assertIsNotNone(result)
        self.assertLess(execution_time, 2.0)  # 应该在 2s 内完成
        mock_mint.assert_called_once()

    def test_router_performance_comparison(self):
        """测试路由器性能对比"""
        batch_size = 100
        mints = self.create_batch_mints(batch_size)
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_erc20_mint:
            with patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions') as mock_token_2022_mint:
                mock_erc20_mint.return_value = "0x123456"
                mock_token_2022_mint.return_value = [MagicMock() for _ in range(batch_size)]
                
                # 测试 ERC20 性能
                _, erc20_time = self.measure_execution_time(
                    TokenizationCalldataRouter.build_mint_calldata,
                    self.erc20_token, mints
                )
                
                # 测试 Token 2022 性能
                _, token_2022_time = self.measure_execution_time(
                    TokenizationCalldataRouter.build_mint_calldata,
                    self.token_2022, mints
                )
                
                # 验证两者性能都在合理范围内
                self.assertLess(erc20_time, 1.0)
                self.assertLess(token_2022_time, 1.0)
                
                # 记录性能差异（用于监控）
                performance_ratio = token_2022_time / erc20_time if erc20_time > 0 else 1
                self.assertLess(performance_ratio, 10)  # Token 2022 不应该比 ERC20 慢 10 倍以上

    def test_memory_usage_scaling(self):
        """测试内存使用扩展性"""
        import gc
        import sys
        
        batch_sizes = [10, 100, 1000]
        memory_usage = []
        
        for batch_size in batch_sizes:
            gc.collect()  # 清理垃圾回收
            
            # 记录开始内存
            start_objects = len(gc.get_objects())
            
            mints = self.create_batch_mints(batch_size)
            
            with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
                mock_mint.return_value = "0x123456"
                
                result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
                
                # 记录结束内存
                end_objects = len(gc.get_objects())
                memory_diff = end_objects - start_objects
                memory_usage.append(memory_diff)
                
                # 清理
                del mints
                del result
                gc.collect()
        
        # 验证内存使用是线性增长的（不是指数增长）
        if len(memory_usage) >= 2:
            growth_ratio = memory_usage[1] / memory_usage[0] if memory_usage[0] > 0 else 1
            self.assertLess(growth_ratio, 50)  # 内存增长应该是合理的

    def test_concurrent_performance(self):
        """测试并发性能"""
        import threading
        import time
        
        batch_size = 50
        thread_count = 10
        results = []
        execution_times = []
        
        def mint_operation():
            mints = self.create_batch_mints(batch_size)
            
            with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
                mock_mint.return_value = "0x123456"
                
                start_time = time.time()
                result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
                end_time = time.time()
                
                results.append(result)
                execution_times.append(end_time - start_time)
        
        # 创建并启动线程
        threads = []
        start_time = time.time()
        
        for _ in range(thread_count):
            thread = threading.Thread(target=mint_operation)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 验证结果
        self.assertEqual(len(results), thread_count)
        self.assertEqual(len(execution_times), thread_count)
        
        # 验证并发性能
        avg_execution_time = sum(execution_times) / len(execution_times)
        self.assertLess(avg_execution_time, 1.0)  # 平均执行时间应该合理
        self.assertLess(total_time, 5.0)  # 总时间应该合理

    def test_repeated_operations_performance(self):
        """测试重复操作性能"""
        batch_size = 100
        repeat_count = 50
        mints = self.create_batch_mints(batch_size)
        
        execution_times = []
        
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x123456"
            
            for _ in range(repeat_count):
                _, execution_time = self.measure_execution_time(
                    TokenizationCalldataRouter.build_mint_calldata,
                    self.erc20_token, mints
                )
                execution_times.append(execution_time)
        
        # 验证性能稳定性
        avg_time = sum(execution_times) / len(execution_times)
        max_time = max(execution_times)
        min_time = min(execution_times)
        
        self.assertLess(avg_time, 0.5)  # 平均时间应该合理
        self.assertLess(max_time / min_time, 10)  # 最大最小时间比例应该合理（性能稳定）

    def test_different_operation_types_performance(self):
        """测试不同操作类型的性能"""
        batch_size = 100
        
        # 准备数据
        mints = self.create_batch_mints(batch_size)
        burns = self.create_batch_burns(batch_size)
        addresses = self.create_batch_addresses(batch_size)
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        operation_times = {}
        
        # 测试 mint 性能
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata') as mock_mint:
            mock_mint.return_value = "0x123456"
            _, mint_time = self.measure_execution_time(
                TokenizationCalldataRouter.build_mint_calldata,
                self.erc20_token, mints
            )
            operation_times['mint'] = mint_time
        
        # 测试 burn 性能
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_burn_calldata') as mock_burn:
            mock_burn.return_value = "0x789abc"
            _, burn_time = self.measure_execution_time(
                TokenizationCalldataRouter.build_burn_calldata,
                self.erc20_token, burns, self.address
            )
            operation_times['burn'] = burn_time
        
        # 测试 allowlist 性能
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_allowlist_calldata') as mock_allowlist:
            mock_allowlist.return_value = "0xdef456"
            _, allowlist_time = self.measure_execution_time(
                TokenizationCalldataRouter.build_allowlist_calldata,
                self.erc20_token, addresses, operation
            )
            operation_times['allowlist'] = allowlist_time
        
        # 测试 pause 性能
        with patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_pause_calldata') as mock_pause:
            mock_pause.return_value = "0x8c379a00"
            _, pause_time = self.measure_execution_time(
                TokenizationCalldataRouter.build_pause_calldata,
                self.erc20_token
            )
            operation_times['pause'] = pause_time
        
        # 验证所有操作性能都在合理范围内
        for operation_name, execution_time in operation_times.items():
            self.assertLess(execution_time, 1.0, f"{operation_name} operation took too long: {execution_time}s")
