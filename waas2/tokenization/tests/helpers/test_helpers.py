"""
测试辅助工具
提供便捷的测试数据创建和验证方法
"""

import uuid
from typing import Dict, List, Any, Union
from unittest.mock import MagicMock

import cobo_waas2

from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.tests.validators.calldata_validator import TokenizationValidator
from waas2.transactions.dev.bo.transaction_query.destination import SolContractCallInstruction


class TokenizationTestHelper:
    """Tokenization 测试辅助类"""
    
    @staticmethod
    def create_mock_token(
        token_id: str = None,
        chain_id: str = "ETH",
        token_address: str = "******************************************",
        standard: str = None,
        decimals: int = 18,
        token_access_activated: bool = False
    ) -> MagicMock:
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=token_id or f"TEST_TOKEN_{uuid.uuid4().hex[:8]}",
            chain_id=chain_id,
            token_address=token_address,
            standard=standard or cobo_waas2.TokenizationTokenStandard.ERC20,
            decimals=decimals,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )
    
    @staticmethod
    def create_mock_mint_params(
        addresses: List[str] = None,
        amounts: List[float] = None
    ) -> List[MagicMock]:
        """创建 mock mint 参数"""
        if addresses is None:
            addresses = ["******************************************"]
        if amounts is None:
            amounts = [1000.0]
        
        mints = []
        for addr, amount in zip(addresses, amounts):
            mints.append(MagicMock(to_address=addr, amount=amount))
        return mints
    
    @staticmethod
    def create_mock_burn_params(
        addresses: List[str] = None,
        amounts: List[float] = None
    ) -> List[MagicMock]:
        """创建 mock burn 参数"""
        if addresses is None:
            addresses = ["******************************************"]
        if amounts is None:
            amounts = [500.0]
        
        burns = []
        for addr, amount in zip(addresses, amounts):
            burns.append(MagicMock(from_address=addr, amount=amount))
        return burns
    
    @staticmethod
    def create_validation_params(
        token_address: str,
        chain_id: str,
        operation_type: str,
        **kwargs
    ) -> Dict[str, Any]:
        """创建验证参数"""
        params = {
            "token_address": token_address,
            "chain_id": chain_id,
            "operation_type": operation_type
        }
        params.update(kwargs)
        return params


class CalldataTestMixin:
    """Calldata 测试混入类"""
    
    def assert_calldata_valid(
        self,
        result: Union[str, List[SolContractCallInstruction]],
        operation_type: str,
        token_standard: str,
        validation_params: Dict[str, Any],
        msg: str = None
    ):
        """断言 calldata/instructions 有效"""
        validation_result = TokenizationValidator.validate_calldata_or_instructions(
            result, operation_type, token_standard, validation_params
        )
        
        error_msg = self._format_validation_error(validation_result, msg)
        self.assertTrue(validation_result["valid"], error_msg)
        
        # 如果有警告，记录但不失败
        if validation_result.get("warnings"):
            print(f"Validation warnings: {validation_result['warnings']}")
    
    def assert_calldata_invalid(
        self,
        result: Union[str, List[SolContractCallInstruction]],
        operation_type: str,
        token_standard: str,
        validation_params: Dict[str, Any],
        expected_error: str = None,
        msg: str = None
    ):
        """断言 calldata/instructions 无效"""
        validation_result = TokenizationValidator.validate_calldata_or_instructions(
            result, operation_type, token_standard, validation_params
        )
        
        self.assertFalse(validation_result["valid"], 
                        msg or "Expected calldata/instructions to be invalid")
        
        if expected_error:
            error_found = any(expected_error in error for error in validation_result.get("errors", []))
            self.assertTrue(error_found, 
                          f"Expected error '{expected_error}' not found in {validation_result['errors']}")
    
    def get_validation_details(
        self,
        result: Union[str, List[SolContractCallInstruction]],
        operation_type: str,
        token_standard: str,
        validation_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取详细的验证结果"""
        return TokenizationValidator.validate_calldata_or_instructions(
            result, operation_type, token_standard, validation_params
        )
    
    def _format_validation_error(self, validation_result: Dict[str, Any], msg: str = None) -> str:
        """格式化验证错误信息"""
        base_msg = msg or "Calldata/Instructions validation failed"
        errors = validation_result.get("errors", [])
        warnings = validation_result.get("warnings", [])
        
        error_details = []
        if errors:
            error_details.append(f"Errors: {errors}")
        if warnings:
            error_details.append(f"Warnings: {warnings}")
        
        if error_details:
            return f"{base_msg}. {'; '.join(error_details)}"
        return base_msg


class MockContractHelper:
    """Mock 合约辅助类"""
    
    @staticmethod
    def create_mock_erc20_contract():
        """创建 mock ERC20 合约"""
        mock_contract = MagicMock()
        
        # Mock decode_function_input 方法
        def mock_decode_function_input(calldata):
            # 简单的 mock 实现，根据 calldata 返回不同的结果
            # 对于任何 calldata，都返回 mint 函数（用于测试）
            function_obj = MagicMock()
            function_obj.function_identifier = "mint"
            return function_obj, {
                "recipients": ["******************************************"],
                "amounts": [1000000000000000000]  # 1 token with 18 decimals
            }
        
        mock_contract.decode_function_input = mock_decode_function_input
        return mock_contract
    
    @staticmethod
    def create_mock_sol_instruction():
        """创建 mock Solana 指令"""
        from waas2.transactions.dev.bo.transaction_query.destination import SolContractCallAccount
        
        return SolContractCallInstruction(
            accounts=[
                SolContractCallAccount(
                    pubkey="So********************************111111112",
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="********************************",
                    is_signer=False,
                    is_writable=True
                ),
                SolContractCallAccount(
                    pubkey="22222222222222222222222222222222",
                    is_signer=True,
                    is_writable=False
                )
            ],
            data="dGVzdCBkYXRh",  # base64 encoded "test data"
            program_id="TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
        )


class ValidationTestCase:
    """验证测试用例基类"""
    
    def setUp(self):
        """设置测试环境"""
        self.helper = TokenizationTestHelper()
        self.mock_contract_helper = MockContractHelper()
    
    def create_erc20_validation_params(
        self,
        operation_type: str,
        token_address: str = "******************************************",
        chain_id: str = "ETH",
        **kwargs
    ) -> Dict[str, Any]:
        """创建 ERC20 验证参数"""
        return self.helper.create_validation_params(
            token_address=token_address,
            chain_id=chain_id,
            operation_type=operation_type,
            **kwargs
        )
    
    def create_token_2022_validation_params(
        self,
        operation_type: str,
        token_address: str = "So********************************111111112",
        chain_id: str = "SOLDEV_SOL",
        **kwargs
    ) -> Dict[str, Any]:
        """创建 Token 2022 验证参数"""
        return self.helper.create_validation_params(
            token_address=token_address,
            chain_id=chain_id,
            operation_type=operation_type,
            **kwargs
        )
    
    def run_validation_test(
        self,
        result: Union[str, List[SolContractCallInstruction]],
        operation_type: str,
        token_standard: str,
        validation_params: Dict[str, Any],
        should_be_valid: bool = True,
        expected_error: str = None
    ) -> Dict[str, Any]:
        """运行验证测试"""
        validation_result = TokenizationValidator.validate_calldata_or_instructions(
            result, operation_type, token_standard, validation_params
        )
        
        if should_be_valid:
            assert validation_result["valid"], f"Validation failed: {validation_result['errors']}"
        else:
            assert not validation_result["valid"], "Expected validation to fail"
            if expected_error:
                error_found = any(expected_error in error for error in validation_result.get("errors", []))
                assert error_found, f"Expected error '{expected_error}' not found"
        
        return validation_result
