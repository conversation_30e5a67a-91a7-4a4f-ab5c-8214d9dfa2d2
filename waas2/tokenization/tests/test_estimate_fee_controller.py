from unittest.mock import Mock, patch

import cobo_waas2
from django.test import TestCase

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.controllers.estimate_fee import (
    TokenizationEstimateFeeController,
)
from waas2.tokenization.dao.tokenization import TokenDao
from waas2.transactions.dev.bo.estimate_fee import (
    EstimateFeeEVMEIP1559,
    EstimateFeeEVMEIP1559Base,
    EstimateFeeEVMLegacy,
    EstimateFeeEVMLegacyBase,
)


class TestTokenizationEstimateFeeController(TestCase):
    """TokenizationEstimateFeeController 测试类"""

    def setUp(self):
        """设置测试数据"""
        super().setUp()

        # 基础测试数据
        self.org_id = "test-org-id"
        self.biz_org_id = 123
        self.token_id = "TEST_TOKEN"
        self.wallet_id = "test-wallet-id"
        self.address = "******************************************"
        self.address_2 = "******************************************"
        self.address_3 = "******************************************"
        self.chain_id = "ETH"
        self.token_address = "******************************************"
        self.factory_address = "0xfactory1234567890abcdef1234567890abcdef12"
        self.calldata = "0x123456"

        # Mock 费用估算响应
        self.mock_estimated_fee = Mock()
        self.mock_estimated_fee.fee_type = cobo_waas2.FeeType.EVM_EIP_1559
        self.mock_estimated_fee.token_id = "ETH"

    def create_mock_request(self, operation_params):
        """创建简化的mock请求对象"""
        request = Mock(spec=cobo_waas2.TokenizationEstimateFeeRequest)
        request.operation_params = Mock()
        request.operation_params.actual_instance = operation_params
        return request

    def create_mock_source(
        self, source_type=cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
    ):
        """创建mock源对象"""
        source = Mock()
        source.source_type = source_type
        source.wallet_id = self.wallet_id
        source.address = self.address
        return source

    def create_mock_deploy_operation_params(self, source_type=None):
        """创建部署操作参数mock"""
        params = Mock(spec=cobo_waas2.TokenizationIssueEstimateFeeParams)
        params.operation_type = cobo_waas2.TokenizationOperationType.ISSUE
        params.chain_id = self.chain_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source(
            source_type
            if source_type
            else cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        )
        params.token_params = Mock()
        params.token_params.actual_instance = Mock()
        return params

    def create_mock_mint_operation_params(self, mints=None):
        """创建铸币操作参数mock"""
        params = Mock(spec=cobo_waas2.TokenizationMintEstimateFeeParams)
        params.operation_type = cobo_waas2.TokenizationOperationType.MINT
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()

        if mints is None:
            # 创建真实的mint对象而不是Mock
            mint_mock = Mock()
            mint_mock.to_address = "******************************************"
            mint_mock.amount = "1000"
            params.mints = [mint_mock]
        else:
            params.mints = mints
        return params

    def create_mock_burn_operation_params(self, burns=None):
        """创建销毁操作参数mock"""
        params = Mock(spec=cobo_waas2.TokenizationBurnEstimateFeeParams)
        params.operation_type = cobo_waas2.TokenizationOperationType.BURN
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()
        if burns is None:
            burn_mock = Mock()
            burn_mock.from_address = self.address
            burn_mock.amount = "100"
            params.burns = [burn_mock]
        else:
            params.burns = burns
        return params

    def create_mock_pause_unpause_operation_params(self, operation_type):
        """创建暂停/取消暂停操作参数mock"""
        params = Mock(
            spec=(
                cobo_waas2.TokenizationPauseEstimateFeeParams
                if operation_type == cobo_waas2.TokenizationOperationType.PAUSE
                else cobo_waas2.TokenizationUnpauseEstimateFeeParams
            )
        )
        params.operation_type = operation_type
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()
        return params

    def create_mock_allowlist_operation_params(self, addresses=None, action=None):
        """创建更新白名单操作参数mock"""
        params = Mock(
            spec=cobo_waas2.TokenizationUpdateAllowlistAddressesEstimateFeeParams
        )
        params.operation_type = (
            cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES
        )
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()
        params.addresses = (
            [
                cobo_waas2.TokenizationUpdateAllowlistAddressesParamsAddressesInner(
                    address=a
                )
                for a in addresses
            ]
            if addresses is not None
            else [
                cobo_waas2.TokenizationUpdateAllowlistAddressesParamsAddressesInner(
                    address=self.address_2
                ),
                cobo_waas2.TokenizationUpdateAllowlistAddressesParamsAddressesInner(
                    address=self.address_3
                ),
            ]
        )
        params.action = (
            action
            if action is not None
            else cobo_waas2.TokenizationUpdateAddressAction.GRANT
        )
        return params

    def create_mock_blocklist_operation_params(self, addresses=None, action=None):
        """创建更新黑名单操作参数mock"""
        params = Mock(
            spec=cobo_waas2.TokenizationUpdateBlocklistAddressesEstimateFeeParams
        )
        params.operation_type = (
            cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES
        )
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()
        params.addresses = (
            [
                cobo_waas2.TokenizationUpdateBlocklistAddressesParamsAddressesInner(
                    address=a
                )
                for a in addresses
            ]
            if addresses is not None
            else [
                cobo_waas2.TokenizationUpdateBlocklistAddressesParamsAddressesInner(
                    address=self.address_2
                ),
                cobo_waas2.TokenizationUpdateBlocklistAddressesParamsAddressesInner(
                    address=self.address_3
                ),
            ]
        )
        params.action = (
            action
            if action is not None
            else cobo_waas2.TokenizationUpdateAddressAction.GRANT
        )
        return params

    def create_mock_toggle_allowlist_operation_params(self, activation=None):
        """创建切换白名单功能操作参数mock"""
        params = Mock(spec=cobo_waas2.TokenizationToggleAllowlistEstimateFeeParams)
        params.operation_type = cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST
        params.token_id = self.token_id
        params.source = Mock()
        params.source.actual_instance = self.create_mock_source()
        params.activation = activation if activation is not None else True
        return params

    def create_test_token(self, **kwargs):
        """创建测试代币"""
        return TokenDao.create(
            org_id=kwargs.get("org_id", self.org_id),
            token_id=kwargs.get("token_id", self.token_id),
            chain_id=kwargs.get("chain_id", self.chain_id),
            token_address=kwargs.get("token_address", self.token_address),
            status=kwargs.get("status", cobo_waas2.TokenizationStatus.ACTIVE),
            name=kwargs.get("name", "Test Token"),
            symbol=kwargs.get("symbol", "TEST"),
            decimals=kwargs.get("decimals", 18),
            standard=kwargs.get("standard", cobo_waas2.TokenizationTokenStandard.ERC20),
        )

    # ========== 主要方法测试 ==========

    def test_estimate_fee_unsupported_operation_type(self):
        """测试不支持的操作类型"""
        operation_params = Mock()
        operation_params.operation_type = "INVALID_OPERATION"
        request = self.create_mock_request(operation_params)

        # 执行测试并验证异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationEstimateFeeController.estimate_fee(
                org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
            )

        self.assertIn("Unsupported operation type", str(context.exception))

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.deploy.TokenizationDeployManager.build_deploy_calldata"
    )
    @patch(
        "waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_factory_address"
    )
    def test_estimate_deploy_fee_org_controlled(
        self, mock_get_factory, mock_calldata, mock_estimate
    ):
        """测试部署代币费用估算 - MPC源"""
        # Mock 依赖
        mock_get_factory.return_value = self.factory_address
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee

        # 创建请求参数
        deploy_params = self.create_mock_deploy_operation_params(
            cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        )
        request = self.create_mock_request(deploy_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_get_factory.assert_called_once_with(self.chain_id)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.deploy.TokenizationDeployManager.build_deploy_calldata"
    )
    @patch(
        "waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_factory_address"
    )
    def test_estimate_deploy_fee_web3(
        self, mock_get_factory, mock_calldata, mock_estimate
    ):
        """测试部署代币费用估算 - Web3源"""
        # Mock 依赖
        mock_get_factory.return_value = self.factory_address
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee

        # 创建请求参数
        deploy_params = self.create_mock_deploy_operation_params(
            cobo_waas2.TokenizationOperationSourceType.WEB3
        )
        request = self.create_mock_request(deploy_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_get_factory.assert_called_once_with(self.chain_id)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.tokenization.managers.deploy.TokenizationDeployManager.build_deploy_calldata"
    )
    def test_estimate_deploy_fee_unsupported_source(self, mock_calldata):
        """测试不支持的源类型"""
        # Mock calldata以避免实际调用
        mock_calldata.return_value = self.calldata

        # 创建无效源类型的参数
        deploy_params = self.create_mock_deploy_operation_params()
        deploy_params.source.actual_instance.source_type = "INVALID_SOURCE"

        request = self.create_mock_request(deploy_params)

        # 执行测试并验证异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationEstimateFeeController.estimate_fee(
                org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
            )

        self.assertIn("Unsupported source type", str(context.exception))

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_mint_calldata"
    )
    def test_estimate_mint_fee_success(self, mock_calldata, mock_estimate):
        """测试铸币费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        mint_params = self.create_mock_mint_operation_params()
        request = self.create_mock_request(mint_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    def test_estimate_mint_fee_no_mints(self):
        """测试铸币费用估算 - 没有铸币参数"""
        # 创建没有mints的参数
        mint_params = self.create_mock_mint_operation_params(mints=[])
        self.create_test_token()

        request = self.create_mock_request(mint_params)

        # 执行测试并验证异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationEstimateFeeController.estimate_fee(
                org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
            )

        self.assertIn("mints is required", str(context.exception))

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_burn_calldata"
    )
    def test_estimate_burn_fee_success(self, mock_calldata, mock_estimate):
        """测试销毁费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        burn_params = self.create_mock_burn_operation_params()
        request = self.create_mock_request(burn_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_burn_calldata"
    )
    def test_estimate_burn_fee_default_burns(self, mock_calldata, mock_estimate):
        """测试销毁费用估算 - 使用默认销毁参数"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        burn_params = self.create_mock_burn_operation_params(burns=[])
        request = self.create_mock_request(burn_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_pause_calldata"
    )
    def test_estimate_pause_fee_success(self, mock_calldata, mock_estimate):
        """测试暂停费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        pause_params = self.create_mock_pause_unpause_operation_params(
            cobo_waas2.TokenizationOperationType.PAUSE
        )
        request = self.create_mock_request(pause_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_unpause_calldata"
    )
    def test_estimate_unpause_fee_success(self, mock_calldata, mock_estimate):
        """测试取消暂停费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        unpause_params = self.create_mock_pause_unpause_operation_params(
            cobo_waas2.TokenizationOperationType.UNPAUSE
        )
        request = self.create_mock_request(unpause_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_allowlist_calldata"
    )
    def test_estimate_allowlist_fee_success(self, mock_calldata, mock_estimate):
        """测试更新白名单费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        allowlist_params = self.create_mock_allowlist_operation_params()
        request = self.create_mock_request(allowlist_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_allowlist_calldata"
    )
    def test_estimate_allowlist_fee_default_addresses(
        self, mock_calldata, mock_estimate
    ):
        """测试更新白名单费用估算 - 使用默认地址"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        allowlist_params = self.create_mock_allowlist_operation_params(addresses=[])
        request = self.create_mock_request(allowlist_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_blocklist_calldata"
    )
    def test_estimate_blocklist_fee_success(self, mock_calldata, mock_estimate):
        """测试更新黑名单费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        blocklist_params = self.create_mock_blocklist_operation_params()
        request = self.create_mock_request(blocklist_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_toggle_allowlist_calldata"
    )
    def test_estimate_restriction_fee_success(self, mock_calldata, mock_estimate):
        """测试切换白名单功能费用估算成功"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        toggle_params = self.create_mock_toggle_allowlist_operation_params(
            activation=True
        )
        request = self.create_mock_request(toggle_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.estimate_fee_for_dev_api_v2"
    )
    @patch(
        "waas2.tokenization.managers.calldata.router.TokenizationCalldataRouter.build_toggle_allowlist_calldata"
    )
    def test_estimate_restriction_fee_default_restriction(
        self, mock_calldata, mock_estimate
    ):
        """测试切换白名单功能费用估算 - 使用默认值"""
        # Mock 依赖
        mock_calldata.return_value = self.calldata
        mock_estimate.return_value = self.mock_estimated_fee
        self.create_test_token()

        # 创建请求参数
        toggle_params = self.create_mock_toggle_allowlist_operation_params()
        request = self.create_mock_request(toggle_params)

        # 执行测试
        result = TokenizationEstimateFeeController.estimate_fee(
            org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
        )

        # 验证结果
        self.assertEqual(result, self.mock_estimated_fee)
        mock_calldata.assert_called_once()
        mock_estimate.assert_called_once()

    # ========== 覆盖所有操作类型的测试 ==========
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_deploy_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_mint_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_burn_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_pause_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_unpause_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_allowlist_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_blocklist_fee"
    )
    @patch(
        "waas2.tokenization.controllers.estimate_fee.TokenizationEstimateFeeController._estimate_allowlist_toggle_fee"
    )
    def test_estimate_fee_all_operation_types(self, *mocks):
        """测试所有支持的操作类型都能正确路由"""
        operation_map = {
            cobo_waas2.TokenizationOperationType.ISSUE: self.create_mock_deploy_operation_params(),
            cobo_waas2.TokenizationOperationType.MINT: self.create_mock_mint_operation_params(),
            cobo_waas2.TokenizationOperationType.BURN: self.create_mock_burn_operation_params(),
            cobo_waas2.TokenizationOperationType.PAUSE: self.create_mock_pause_unpause_operation_params(
                cobo_waas2.TokenizationOperationType.PAUSE
            ),
            cobo_waas2.TokenizationOperationType.UNPAUSE: self.create_mock_pause_unpause_operation_params(
                cobo_waas2.TokenizationOperationType.UNPAUSE
            ),
            cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES: self.create_mock_allowlist_operation_params(),
            cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES: self.create_mock_blocklist_operation_params(),
            cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST: self.create_mock_toggle_allowlist_operation_params(),
        }

        for i, (op_type, params) in enumerate(operation_map.items()):
            with self.subTest(operation=op_type.value):
                # 为 MINT, BURN 等需要 token 的操作创建 mock token
                if op_type != cobo_waas2.TokenizationOperationType.ISSUE:
                    self.create_test_token()

                mock_method = mocks[len(mocks) - 1 - i]
                request = self.create_mock_request(params)
                TokenizationEstimateFeeController.estimate_fee(
                    org_id=self.org_id, biz_org_id=self.biz_org_id, params=request
                )
                mock_method.assert_called_once()

    # ========== Gas Buffer 测试 ==========

    def test_apply_gas_buffer_default(self):
        """测试默认 20% gas buffer 计算"""
        # 测试基本的 buffer 计算
        original_gas = 21000
        buffered_gas = TokenizationEstimateFeeController._apply_gas_buffer(
            original_gas, 20.0
        )

        # 验证 buffer 计算：21000 * 1.2 = 25200
        expected_gas = int(21000 * 1.2)
        self.assertEqual(buffered_gas, expected_gas)

        # 测试较大的 gas 值
        large_gas = 500000
        buffered_large_gas = TokenizationEstimateFeeController._apply_gas_buffer(
            large_gas, 20.0
        )
        expected_large_gas = int(500000 * 1.2)
        self.assertEqual(buffered_large_gas, expected_large_gas)

    def test_apply_gas_buffer_custom_percentage(self):
        """测试自定义百分比的 gas buffer 计算"""
        original_gas = 100000

        # 测试 10% buffer
        buffered_gas_10 = TokenizationEstimateFeeController._apply_gas_buffer(
            original_gas, 10.0
        )
        expected_gas_10 = int(100000 * 1.1)
        self.assertEqual(buffered_gas_10, expected_gas_10)

        # 测试 25% buffer
        buffered_gas_25 = TokenizationEstimateFeeController._apply_gas_buffer(
            original_gas, 25.0
        )
        expected_gas_25 = int(100000 * 1.25)
        self.assertEqual(buffered_gas_25, expected_gas_25)

    def test_apply_eip1559_buffer(self):
        """测试 EIP1559 费用结果的 buffer 应用"""
        # 创建原始的 EIP1559 费用结果
        original_gas_limit = "21000"

        slow_fee = EstimateFeeEVMEIP1559Base(
            max_fee_per_gas="20000000000",
            max_priority_fee_per_gas="1000000000",
            gas_limit=original_gas_limit,
        )
        recommended_fee = EstimateFeeEVMEIP1559Base(
            max_fee_per_gas="25000000000",
            max_priority_fee_per_gas="2000000000",
            gas_limit=original_gas_limit,
        )
        fast_fee = EstimateFeeEVMEIP1559Base(
            max_fee_per_gas="30000000000",
            max_priority_fee_per_gas="3000000000",
            gas_limit=original_gas_limit,
        )

        original_result = EstimateFeeEVMEIP1559(
            token_id="ETH",
            slow=slow_fee,
            recommended=recommended_fee,
            fast=fast_fee,
        )

        # 应用 buffer
        buffered_result = TokenizationEstimateFeeController._apply_eip1559_buffer(
            original_result
        )

        # 验证结果
        expected_gas_limit = str(int(21000 * 1.2))

        self.assertEqual(buffered_result.token_id, "ETH")
        self.assertEqual(buffered_result.slow.gas_limit, expected_gas_limit)
        self.assertEqual(buffered_result.recommended.gas_limit, expected_gas_limit)
        self.assertEqual(buffered_result.fast.gas_limit, expected_gas_limit)

        # 验证其他字段保持不变
        self.assertEqual(buffered_result.slow.max_fee_per_gas, "20000000000")
        self.assertEqual(buffered_result.slow.max_priority_fee_per_gas, "1000000000")

    def test_apply_legacy_buffer(self):
        """测试 Legacy 费用结果的 buffer 应用"""
        # 创建原始的 Legacy 费用结果
        original_gas_limit = "21000"

        slow_fee = EstimateFeeEVMLegacyBase(
            gas_price="20000000000",
            gas_limit=original_gas_limit,
        )
        recommended_fee = EstimateFeeEVMLegacyBase(
            gas_price="25000000000",
            gas_limit=original_gas_limit,
        )
        fast_fee = EstimateFeeEVMLegacyBase(
            gas_price="30000000000",
            gas_limit=original_gas_limit,
        )

        original_result = EstimateFeeEVMLegacy(
            token_id="ETH",
            slow=slow_fee,
            recommended=recommended_fee,
            fast=fast_fee,
        )

        # 应用 buffer
        buffered_result = TokenizationEstimateFeeController._apply_legacy_buffer(
            original_result
        )

        # 验证结果
        expected_gas_limit = str(int(21000 * 1.2))

        self.assertEqual(buffered_result.token_id, "ETH")
        self.assertEqual(buffered_result.slow.gas_limit, expected_gas_limit)
        self.assertEqual(buffered_result.recommended.gas_limit, expected_gas_limit)
        self.assertEqual(buffered_result.fast.gas_limit, expected_gas_limit)

        # 验证其他字段保持不变
        self.assertEqual(buffered_result.slow.gas_price, "20000000000")
        self.assertEqual(buffered_result.recommended.gas_price, "25000000000")
        self.assertEqual(buffered_result.fast.gas_price, "30000000000")

    def test_apply_deploy_fee_buffer_unknown_type(self):
        """测试未知费用类型的 buffer 应用"""
        # 创建一个未知类型的对象
        unknown_result = Mock()

        # 应用 buffer，应该返回原始对象
        result = TokenizationEstimateFeeController._apply_fee_buffer(unknown_result)

        self.assertEqual(result, unknown_result)
