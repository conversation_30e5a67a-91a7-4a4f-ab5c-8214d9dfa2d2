import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationCalldataRouter(TokenizationTestBase):
    """TokenizationCalldataRouter 路由器测试"""

    def setUp(self):
        super().setUp()
        self.erc20_token = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20
        )
        self.token_2022 = self.create_mock_token(
            standard=cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022,
            chain_id="SOLDEV_SOL"
        )

    def create_mock_token(self, standard=None, chain_id=None, token_access_activated=False):
        """创建 mock Token 对象"""
        return MagicMock(
            token_id=self.token_id,
            chain_id=chain_id or self.chain_id,
            token_address=self.address,
            standard=standard or cobo_waas2.TokenizationTokenStandard.ERC20,
            decimals=18,
            extra=TokenizationExtra(
                token_access_activated=token_access_activated
            ).model_dump(mode="json")
        )

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_mint_calldata')
    def test_build_mint_calldata_erc20(self, mock_erc20_mint):
        """测试 ERC20 mint calldata 路由"""
        mock_erc20_mint.return_value = "0x123456"
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        result = TokenizationCalldataRouter.build_mint_calldata(self.erc20_token, mints)
        
        self.assertEqual(result, "0x123456")
        mock_erc20_mint.assert_called_once_with(self.erc20_token, mints)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions')
    def test_build_mint_calldata_token_2022(self, mock_token_2022_mint):
        """测试 Token 2022 mint instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_mint.return_value = mock_instructions
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        result = TokenizationCalldataRouter.build_mint_calldata(self.token_2022, mints)
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_mint.assert_called_once_with(self.token_2022, mints, "SOLDEV_SOL")

    def test_build_mint_calldata_unsupported_standard(self):
        """测试不支持的代币标准"""
        unsupported_token = self.create_mock_token(standard="UNSUPPORTED")
        mints = [MagicMock(to_address=self.address, amount=100)]
        
        with self.assertRaises(InvalidParamException):
            TokenizationCalldataRouter.build_mint_calldata(unsupported_token, mints)

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_burn_calldata')
    def test_build_burn_calldata_erc20(self, mock_erc20_burn):
        """测试 ERC20 burn calldata 路由"""
        mock_erc20_burn.return_value = "0x789abc"
        burns = [MagicMock(from_address=self.address, amount=50)]
        
        result = TokenizationCalldataRouter.build_burn_calldata(
            self.erc20_token, burns, self.address
        )
        
        self.assertEqual(result, "0x789abc")
        mock_erc20_burn.assert_called_once_with(self.erc20_token, burns, self.address)

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_burn_instructions')
    def test_build_burn_calldata_token_2022(self, mock_token_2022_burn):
        """测试 Token 2022 burn instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_burn.return_value = mock_instructions
        burns = [MagicMock(from_address=self.address, amount=50)]
        
        result = TokenizationCalldataRouter.build_burn_calldata(
            self.token_2022, burns, self.address
        )
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_burn.assert_called_once_with(
            self.token_2022, burns, self.address, "SOLDEV_SOL"
        )

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_allowlist_calldata')
    def test_build_allowlist_calldata_erc20(self, mock_erc20_allowlist):
        """测试 ERC20 allowlist calldata 路由"""
        mock_erc20_allowlist.return_value = "0xdef123"
        addresses = [self.address]
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        result = TokenizationCalldataRouter.build_allowlist_calldata(
            self.erc20_token, addresses, operation
        )
        
        self.assertEqual(result, "0xdef123")
        mock_erc20_allowlist.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address, addresses, operation
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_thaw_account_instructions')
    def test_build_allowlist_calldata_token_2022(self, mock_token_2022_thaw):
        """测试 Token 2022 allowlist (thaw) instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_thaw.return_value = mock_instructions
        addresses = [self.address]
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        result = TokenizationCalldataRouter.build_allowlist_calldata(
            self.token_2022, addresses, operation
        )
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_thaw.assert_called_once_with(self.token_2022, addresses, "SOLDEV_SOL")

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_blocklist_calldata')
    def test_build_blocklist_calldata_erc20(self, mock_erc20_blocklist):
        """测试 ERC20 blocklist calldata 路由"""
        mock_erc20_blocklist.return_value = "0x456def"
        addresses = [self.address]
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        result = TokenizationCalldataRouter.build_blocklist_calldata(
            self.erc20_token, addresses, operation
        )
        
        self.assertEqual(result, "0x456def")
        mock_erc20_blocklist.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address, addresses, operation
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_freeze_account_instructions')
    def test_build_blocklist_calldata_token_2022(self, mock_token_2022_freeze):
        """测试 Token 2022 blocklist (freeze) instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_freeze.return_value = mock_instructions
        addresses = [self.address]
        operation = cobo_waas2.TokenizationUpdateAddressAction.GRANT
        
        result = TokenizationCalldataRouter.build_blocklist_calldata(
            self.token_2022, addresses, operation
        )
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_freeze.assert_called_once_with(self.token_2022, addresses, "SOLDEV_SOL")

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_pause_calldata')
    def test_build_pause_calldata_erc20(self, mock_erc20_pause):
        """测试 ERC20 pause calldata 路由"""
        mock_erc20_pause.return_value = "0x8c379a00"
        
        result = TokenizationCalldataRouter.build_pause_calldata(self.erc20_token)
        
        self.assertEqual(result, "0x8c379a00")
        mock_erc20_pause.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_pause_instructions')
    def test_build_pause_calldata_token_2022(self, mock_token_2022_pause):
        """测试 Token 2022 pause instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_pause.return_value = mock_instructions
        
        result = TokenizationCalldataRouter.build_pause_calldata(self.token_2022)
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_pause.assert_called_once_with(self.token_2022, "SOLDEV_SOL")

    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_unpause_calldata')
    def test_build_unpause_calldata_erc20(self, mock_erc20_unpause):
        """测试 ERC20 unpause calldata 路由"""
        mock_erc20_unpause.return_value = "0x3f4ba83a"
        
        result = TokenizationCalldataRouter.build_unpause_calldata(self.erc20_token)
        
        self.assertEqual(result, "0x3f4ba83a")
        mock_erc20_unpause.assert_called_once_with(
            self.erc20_token.chain_id, self.erc20_token.token_address
        )

    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_unpause_instructions')
    def test_build_unpause_calldata_token_2022(self, mock_token_2022_unpause):
        """测试 Token 2022 unpause (resume) instructions 路由"""
        mock_instructions = [MagicMock()]
        mock_token_2022_unpause.return_value = mock_instructions
        
        result = TokenizationCalldataRouter.build_unpause_calldata(self.token_2022)
        
        self.assertEqual(result, mock_instructions)
        mock_token_2022_unpause.assert_called_once_with(self.token_2022, "SOLDEV_SOL")

    def test_build_toggle_allowlist_calldata_token_2022_unsupported(self):
        """测试 Token 2022 不支持 toggle allowlist"""
        with self.assertRaises(InvalidParamException):
            TokenizationCalldataRouter.build_toggle_allowlist_calldata(self.token_2022, True)

    def test_chain_id_parameter_override(self):
        """测试 chain_id 参数覆盖"""
        with patch('waas2.tokenization.managers.calldata.solana.token_2022.Token2022InstructionManager.build_mint_instructions') as mock_mint:
            mock_mint.return_value = []
            mints = [MagicMock(to_address=self.address, amount=100)]
            
            # 使用自定义 chain_id
            TokenizationCalldataRouter.build_mint_calldata(
                self.token_2022, mints, chain_id="CUSTOM_CHAIN"
            )
            
            mock_mint.assert_called_once_with(self.token_2022, mints, "CUSTOM_CHAIN")
