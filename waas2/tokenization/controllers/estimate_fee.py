import logging

import cobo_waas2
from cobo_libs.coin import codes
from web3 import Web3

from custody.cobo.utils import decimal_to_string
from waas2.coins.managers_v2 import WaaSTokenManager
from waas2.developers.exceptions import InvalidParamException
from waas2.smart_contract.utils.web3 import web3_instance, web3_provider
from waas2.tokenization.dao.tokenization import TokenDao
from waas2.tokenization.managers.burn import TokenizationBurnManager
from waas2.tokenization.managers.deploy import TokenizationDeployManager
from waas2.tokenization.managers.mint import TokenizationMintManager
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.transactions.dev.bo.estimate_fee import (
    EstimateFeeEVMEIP1559,
    EstimateFeeEVMEIP1559Base,
    EstimateFeeEVMLegacy,
    EstimateFeeEVMLegacyBase,
)
from waas2.transactions.dev.controllers.transaction import TransactionController
from waas2.transactions.dev.enums.fee_type import FeeType
from waas2.transactions.dev.utils import get_fee_type_by_chain_id

logger = logging.getLogger("waas2.tokenization")


class TokenizationEstimateFeeController:
    @staticmethod
    def _apply_gas_buffer(gas_limit: int, buffer_percentage) -> int:
        """
        为 gas limit 添加 buffer

        Args:
            gas_limit: 原始估算的 gas limit
            buffer_percentage: buffer 百分比，默认 20%

        Returns:
            添加 buffer 后的 gas limit
        """
        buffered_gas = int(gas_limit * (1 + buffer_percentage / 100))

        logger.info(
            f"Applied gas buffer: original={gas_limit}, buffer={buffer_percentage}%, buffered={buffered_gas}"
        )
        return buffered_gas

    @classmethod
    def _apply_fee_buffer(
        cls, fee_result: cobo_waas2.EstimatedFee, buffer_percentage: float = 20.0
    ):
        """
        为估算结果添加 buffer

        Args:
            fee_result: 费用估算结果，可能是 EstimateFeeEVMEIP1559 或 EstimateFeeEVMLegacy

        Returns:
            添加 buffer 后的费用估算结果
        """
        fee = fee_result.actual_instance
        if isinstance(fee, cobo_waas2.EstimatedEvmEip1559Fee):
            return cls._apply_eip1559_buffer(fee, buffer_percentage)
        elif isinstance(fee, cobo_waas2.EstimatedEvmLegacyFee):
            return cls._apply_legacy_buffer(fee, buffer_percentage)
        else:
            logger.warning(f"Unknown fee result type: {type(fee_result)}")
            return fee_result

    @classmethod
    def _apply_eip1559_buffer(
        cls,
        fee_result: cobo_waas2.EstimatedEvmEip1559Fee,
        buffer_percentage: float = 20.0,
    ) -> cobo_waas2.EstimatedEvmEip1559Fee:
        """为 EIP1559 费用结果添加 buffer"""

        def apply_buffer_to_eip1559_base(
            base_fee: cobo_waas2.EstimatedEvmEip1559FeeSlow,
        ) -> cobo_waas2.EstimatedEvmEip1559FeeSlow:
            original_gas_limit = int(base_fee.gas_limit)
            buffered_gas_limit = cls._apply_gas_buffer(
                original_gas_limit, buffer_percentage
            )

            return cobo_waas2.EstimatedEvmEip1559FeeSlow(
                max_fee_per_gas=base_fee.max_fee_per_gas,
                max_priority_fee_per_gas=base_fee.max_priority_fee_per_gas,
                gas_limit=decimal_to_string(buffered_gas_limit),
            )

        return cobo_waas2.EstimatedEvmEip1559Fee(
            fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
            token_id=fee_result.token_id,
            slow=apply_buffer_to_eip1559_base(fee_result.slow),
            recommended=apply_buffer_to_eip1559_base(fee_result.recommended),
            fast=apply_buffer_to_eip1559_base(fee_result.fast),
        )

    @classmethod
    def _apply_legacy_buffer(
        cls,
        fee_result: cobo_waas2.EstimatedEvmLegacyFee,
        buffer_percentage: float = 20.0,
    ) -> cobo_waas2.EstimatedEvmLegacyFee:
        """为 Legacy 费用结果添加 buffer"""

        def apply_buffer_to_legacy_base(
            base_fee: cobo_waas2.EstimatedEvmLegacyFeeSlow,
        ) -> cobo_waas2.EstimatedEvmLegacyFeeSlow:
            original_gas_limit = int(base_fee.gas_limit)
            buffered_gas_limit = cls._apply_gas_buffer(
                original_gas_limit, buffer_percentage
            )

            return cobo_waas2.EstimatedEvmLegacyFeeSlow(
                gas_price=base_fee.gas_price,
                gas_limit=decimal_to_string(buffered_gas_limit),
            )

        return cobo_waas2.EstimatedEvmLegacyFee(
            fee_type=cobo_waas2.FeeType.EVM_LEGACY,
            token_id=fee_result.token_id,
            slow=apply_buffer_to_legacy_base(fee_result.slow),
            recommended=apply_buffer_to_legacy_base(fee_result.recommended),
            fast=apply_buffer_to_legacy_base(fee_result.fast),
        )

    @classmethod
    def estimate_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationEstimateFeeRequest,
    ):
        """估算代币操作费用"""
        fee_params = params.operation_params.actual_instance
        if fee_params.operation_type == cobo_waas2.TokenizationOperationType.ISSUE:
            estimated_fee = cls._estimate_deploy_fee(org_id, biz_org_id, fee_params)
        elif fee_params.operation_type == cobo_waas2.TokenizationOperationType.MINT:
            estimated_fee = cls._estimate_mint_fee(org_id, biz_org_id, fee_params)
        elif fee_params.operation_type == cobo_waas2.TokenizationOperationType.BURN:
            estimated_fee = cls._estimate_burn_fee(org_id, biz_org_id, fee_params)
        elif fee_params.operation_type == cobo_waas2.TokenizationOperationType.PAUSE:
            estimated_fee = cls._estimate_pause_fee(org_id, biz_org_id, fee_params)
        elif fee_params.operation_type == cobo_waas2.TokenizationOperationType.UNPAUSE:
            estimated_fee = cls._estimate_unpause_fee(org_id, biz_org_id, fee_params)
        elif (
            fee_params.operation_type
            == cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES
        ):
            estimated_fee = cls._estimate_allowlist_fee(org_id, biz_org_id, fee_params)
        elif (
            fee_params.operation_type
            == cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES
        ):
            estimated_fee = cls._estimate_blocklist_fee(org_id, biz_org_id, fee_params)
        elif (
            fee_params.operation_type
            == cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST
        ):
            estimated_fee = cls._estimate_allowlist_toggle_fee(
                org_id, biz_org_id, fee_params
            )
        elif (
            fee_params.operation_type
            == cobo_waas2.TokenizationOperationType.CONTRACTCALL
        ):
            estimated_fee = cls._estimate_contract_call_fee(
                org_id, biz_org_id, fee_params
            )
        else:
            raise InvalidParamException(
                f"Unsupported operation type: {fee_params.operation_type}"
            )
        return cls._apply_fee_buffer(estimated_fee)

    @classmethod
    def _estimate_deploy_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationIssueEstimateFeeParams,
    ):
        """构建代币部署的费用估算参数"""
        return cls._estimate_deploy_fee_via_waas(org_id, biz_org_id, params)

    @classmethod
    def get_additional_gas_limit(cls, chain_id: str) -> int:
        if chain_id == codes.COIN_ETH:
            return 30000
        return 0

    # deprecated
    @classmethod
    def _estimate_deploy_fee_with_web3(
        cls,
        params: cobo_waas2.TokenizationIssueEstimateFeeParams,
    ):
        """使用 web3.py 方式预估 deploy 手续费"""
        # 检查是否有可用的 web3 节点
        provider = web3_provider(params.chain_id)
        if not provider:
            return None

        try:
            # 获取 web3 实例
            web3 = web3_instance(params.chain_id, provider)
            if not web3.is_connected():
                return None

            # 构建 calldata
            from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
            calldata = TokenizationCalldataRouter.build_deploy_calldata(
                params.chain_id,
                params.source.actual_instance.address,
                params,
            )

            # 获取工厂合约地址
            factory_address = CoboERC20ContractHelper.get_factory_address(
                params.chain_id
            )
            from_address = Web3.to_checksum_address(
                params.source.actual_instance.address
            )

            # 构建交易参数
            transaction = {
                "from": from_address,
                "to": Web3.to_checksum_address(factory_address),
                "data": calldata,
                "value": 0,
            }

            # 使用 web3 预估 gas
            estimated_gas = web3.eth.estimate_gas(transaction)
            additional_gas = cls.get_additional_gas_limit(params.chain_id)
            estimated_gas = max(
                int(estimated_gas * 1.5), estimated_gas + additional_gas
            )

            # 获取链支持的费用类型
            fee_type = get_fee_type_by_chain_id(params.chain_id)
            if not fee_type:
                logger.warning(f"Unsupported fee type for chain {params.chain_id}")
                return None

            # 获取费用 token 信息
            fee_token = WaaSTokenManager.get_fee_token(params.chain_id)
            if not fee_token:
                logger.warning(f"Cannot get fee token for chain {params.chain_id}")
                return None

            # 根据费用类型构建返回结果
            if fee_type == FeeType.EVM_EIP_1559:
                fee = cls._build_eip1559_fee_result(
                    web3, estimated_gas, fee_token.token_id
                )
            elif fee_type == FeeType.EVM_LEGACY:
                fee = cls._build_legacy_fee_result(
                    web3, estimated_gas, fee_token.token_id
                )
            else:
                logger.warning(f"Unsupported fee type {fee_type} for web3 estimation")
                return None

            return cobo_waas2.EstimatedFee.from_dict(fee.to_dict())

        except Exception:
            logger.warning("Web3 fee estimation error", exc_info=True)
            return None

    @classmethod
    def _build_eip1559_fee_result(cls, web3, estimated_gas, fee_token_id):
        """构建 EIP1559 费用结果"""
        try:
            # 获取当前 gas price 作为基准
            gas_price = web3.eth.gas_price

            # 计算不同级别的费用（对齐bc逻辑）
            slow_max_priority_fee_per_gas = int(gas_price * 0.2)
            slow_max_fee_per_gas = int(gas_price * 1.2)

            standard_max_priority_fee_per_gas = int(gas_price * 0.5)
            standard_max_fee_per_gas = int(gas_price * 1.5)

            fast_max_priority_fee_per_gas = int(gas_price * 1.0)
            fast_max_fee_per_gas = int(gas_price * 2.0)

            # 构建费用结构
            slow_fee = EstimateFeeEVMEIP1559Base(
                max_fee_per_gas=decimal_to_string(slow_max_fee_per_gas),
                max_priority_fee_per_gas=decimal_to_string(
                    slow_max_priority_fee_per_gas
                ),
                gas_limit=decimal_to_string(estimated_gas),
            )
            recommended_fee = EstimateFeeEVMEIP1559Base(
                max_fee_per_gas=decimal_to_string(standard_max_fee_per_gas),
                max_priority_fee_per_gas=decimal_to_string(
                    standard_max_priority_fee_per_gas
                ),
                gas_limit=decimal_to_string(estimated_gas),
            )
            fast_fee = EstimateFeeEVMEIP1559Base(
                max_fee_per_gas=decimal_to_string(fast_max_fee_per_gas),
                max_priority_fee_per_gas=decimal_to_string(
                    fast_max_priority_fee_per_gas
                ),
                gas_limit=decimal_to_string(estimated_gas),
            )

            return EstimateFeeEVMEIP1559(
                token_id=fee_token_id,
                slow=slow_fee,
                recommended=recommended_fee,
                fast=fast_fee,
            )

        except Exception as e:
            logger.warning(f"Failed to build EIP1559 fee result: {e}")
            return None

    @classmethod
    def _build_legacy_fee_result(cls, web3, estimated_gas, fee_token_id):
        """构建 Legacy 费用结果"""
        try:
            # 获取当前 gas price
            gas_price = web3.eth.gas_price

            # 计算不同级别的费用（对齐bc逻辑）
            # Slow: 50% of current gas price
            slow_gas_price = int(gas_price * 0.5)

            # Standard: current gas price
            standard_gas_price = gas_price

            # Fast: 150% of current gas price
            fast_gas_price = int(gas_price * 1.5)

            # 构建费用结构
            slow_fee = EstimateFeeEVMLegacyBase(
                gas_price=decimal_to_string(slow_gas_price),
                gas_limit=decimal_to_string(estimated_gas),
            )
            recommended_fee = EstimateFeeEVMLegacyBase(
                gas_price=decimal_to_string(standard_gas_price),
                gas_limit=decimal_to_string(estimated_gas),
            )
            fast_fee = EstimateFeeEVMLegacyBase(
                gas_price=decimal_to_string(fast_gas_price),
                gas_limit=decimal_to_string(estimated_gas),
            )

            return EstimateFeeEVMLegacy(
                token_id=fee_token_id,
                slow=slow_fee,
                recommended=recommended_fee,
                fast=fast_fee,
            )

        except Exception as e:
            logger.warning(f"Failed to build Legacy fee result: {e}")
            return None

    @classmethod
    def _estimate_deploy_fee_via_waas(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationIssueEstimateFeeParams,
    ):
        """原有的代币部署费用估算方法"""
        # 使用统一的合约调用构造方法
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
        calldata = TokenizationCalldataRouter.build_deploy_calldata(
            params.chain_id,
            params.source.actual_instance.address,
            params,
        )

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=params.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=CoboERC20ContractHelper.get_factory_address(
                        params.chain_id
                    ),
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_mint_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationMintEstimateFeeParams,
    ):
        """构建铸币的费用估算参数"""
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        token_address = token.token_address
        mints = params.mints
        if not mints:
            raise InvalidParamException("mints is required")

        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
        calldata = TokenizationCalldataRouter.build_mint_calldata(token, mints)

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_burn_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationBurnEstimateFeeParams,
    ):
        """构建销毁的费用估算参数"""
        burns = params.burns
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        token_address = token.token_address

        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter
        calldata = TokenizationCalldataRouter.build_burn_calldata(
            token, burns, params.source.actual_instance.address
        )

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_pause_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationPauseEstimateFeeParams,
    ):
        """构建暂停的费用估算参数"""
        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter

        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        calldata = TokenizationCalldataRouter.build_pause_calldata(token)

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token.token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_unpause_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationUnpauseEstimateFeeParams,
    ):
        """构建恢复的费用估算参数"""
        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter

        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        calldata = TokenizationCalldataRouter.build_unpause_calldata(token)

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token.token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_allowlist_toggle_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationToggleAllowlistEstimateFeeParams,
    ):
        """构建限制更新的费用估算参数"""
        activation = params.activation
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)

        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter

        calldata = TokenizationCalldataRouter.build_toggle_allowlist_calldata(
            token, activation
        )

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token.token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_allowlist_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationUpdateAllowlistAddressesEstimateFeeParams,
    ):
        """构建白名单更新的费用估算参数"""
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        addresses = params.addresses
        action = params.action

        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter

        addresses_list = [addr.address for addr in addresses]
        calldata = TokenizationCalldataRouter.build_allowlist_calldata(
            token, addresses_list, action
        )

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token.token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_blocklist_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationUpdateBlocklistAddressesEstimateFeeParams,
    ):
        """构建黑名单更新的费用估算参数"""
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        addresses = params.addresses
        action = params.action

        # 使用路由器构建 calldata
        from waas2.tokenization.managers.calldata.router import TokenizationCalldataRouter

        addresses_list = [addr.address for addr in addresses]
        calldata = TokenizationCalldataRouter.build_blocklist_calldata(
            token, addresses_list, action
        )

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token.token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _estimate_contract_call_fee(
        cls,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationContractCallEstimateFeeParams,
    ):
        """构建合约调用的费用估算参数"""
        token = TokenDao.get_by_token_id_and_org_id(params.token_id, org_id)
        token_address = token.token_address
        calldata = params.data.actual_instance.calldata

        source_data = params.source.actual_instance
        if (
            source_data.source_type
            == cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED
        ):
            contract_call_source = cobo_waas2.MpcContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.ORG_CONTROLLED,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        elif source_data.source_type == cobo_waas2.TokenizationOperationSourceType.WEB3:
            contract_call_source = cobo_waas2.CustodialWeb3ContractCallSource(
                source_type=cobo_waas2.ContractCallSourceType.WEB3,
                wallet_id=source_data.wallet_id,
                address=source_data.address,
            )
        else:
            raise InvalidParamException(
                f"Unsupported source type: {source_data.source_type}"
            )

        contract_call_params = cobo_waas2.EstimateContractCallFeeParams(
            request_type=cobo_waas2.EstimateFeeRequestType.CONTRACTCALL,
            chain_id=token.chain_id,
            source=cobo_waas2.ContractCallSource(actual_instance=contract_call_source),
            destination=cobo_waas2.ContractCallDestination(
                cobo_waas2.EvmContractCallDestination(
                    destination_type=cobo_waas2.ContractCallDestinationType.EVM_CONTRACT,
                    address=token_address,
                    calldata=calldata,
                    value="0",
                )
            ),
        )

        return cls._waas_estimate_fee(
            cobo_waas2.EstimateFeeParams(contract_call_params), org_id, biz_org_id
        )

    @classmethod
    def _waas_estimate_fee(
        cls,
        param: cobo_waas2.EstimateFeeParams,
        org_id: str,
        biz_org_id: int,
        enable_validation: bool = False,
    ):
        return TransactionController.estimate_fee_for_dev_api_v2(
            param,
            org_id,
            biz_org_id,
            enable_validation=enable_validation,
        )
