import logging
from typing import Any, Dict, Union

import cobo_waas2

from waas2.devapi.pagination import SortDirection, process_db_pagination
from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import TokenAllowlistDao, TokenRoleDao
from waas2.tokenization.data.params import ListAllowlistAddressesParam
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.managers.allowlist import TokenizationAllowlistManager
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationAllowlistController:
    @classmethod
    def list_allowlist_addresses(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
        params: ListAllowlistAddressesParam,
    ) -> cobo_waas2.TokenizationAllowlistAddressesResponse:
        """获取代币白名单地址列表"""
        # 验证代币存在
        TokenizationUtils.get_and_validate_token(token_id, org_id)

        # 查询白名单地址
        allowlist_addresses = TokenAllowlistDao.list_by_token_id(
            token_id=token_id,
        )

        # 处理分页
        paged_addresses, pagination = process_db_pagination(
            allowlist_addresses,
            direction=SortDirection.from_value(params.direction),
            limit=params.limit,
            before=params.before,
            after=params.after,
        )

        # 转换为响应格式
        address_infos = [
            cobo_waas2.TokenizationAllowlistAddressNote(
                address=addr.address,
                note=addr.note,
                created_timestamp=int(addr.created_time.timestamp() * 1000),
            )
            for addr in paged_addresses
        ]

        return cobo_waas2.TokenizationAllowlistAddressesResponse(
            data=address_infos,
            pagination=cobo_waas2.Pagination.from_dict(pagination.to_dict()),
        )

    @classmethod
    def update_allowlist_addresses(
        cls,
        token_id: str,
        biz_org_id: int,
        org_id: str,
        params: cobo_waas2.TokenizationUpdateAllowlistAddressesRequest,
        api_request_info: dict,
        sign_info: dict,
    ) -> cobo_waas2.TokenizationOperationResponse:
        """更新代币白名单地址"""
        # 验证代币存在且状态为活跃
        token = TokenizationUtils.get_and_validate_token(token_id, org_id)
        TokenizationUtils.validate_source(params.source)
        # 验证操作权限和参数
        cls._validate_allowlist_request(token, params)

        # 调用管理器执行白名单更新
        activity_id = TokenizationAllowlistManager.update_allowlist(
            token=token,
            biz_org_id=biz_org_id,
            org_id=org_id,
            params=params,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        return cobo_waas2.TokenizationOperationResponse(activity_id=str(activity_id))

    @classmethod
    def get_token_access_activation(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
    ) -> cobo_waas2.GetTokenizationAllowlistActivation200Response:
        # 获取并验证 token 信息
        token = TokenizationUtils.get_and_validate_token(token_id, org_id)
        result = TokenizationAllowlistManager.get_token_access_activation(token)
        return cobo_waas2.GetTokenizationAllowlistActivation200Response(
            activated=result
        )

    @classmethod
    def update_token_access_activation(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationAllowlistActivationRequest,
        sign_info: Dict[str, Any],
        api_request_info: Dict[str, Any],
    ) -> cobo_waas2.TokenizationOperationResponse:
        """
        执行代币限制更新操作
        """
        # 获取并验证 token 信息
        token = TokenizationUtils.get_and_validate_token(token_id, org_id)
        TokenizationUtils.validate_source(params.source)
        # 验证操作权限和参数
        cls._validate_allowlist_request(token, params)

        # 调用 manager 执行具体的限制更新逻辑
        activity_id = TokenizationAllowlistManager.update_token_access_activation(
            token=token,
            params=params,
            org_id=org_id,
            biz_org_id=biz_org_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        return cobo_waas2.TokenizationOperationResponse(activity_id=str(activity_id))

    @classmethod
    def _validate_allowlist_request(
        cls,
        token: Token,
        params: Union[
            cobo_waas2.TokenizationAllowlistActivationRequest,
            cobo_waas2.TokenizationUpdateAllowlistAddressesRequest,
        ],
    ):
        """验证限制更新请求的权限和参数"""
        source_data = params.source.actual_instance
        if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
            # 验证操作权限
            if not TokenRoleDao.has_role(
                token.token_id,
                source_data.address,
                TokenizationRole.MANAGER,
            ):
                raise InvalidParamException(
                    f"Address {source_data.address} is not allowed to manage token {token.token_id} allowlist activation."
                )
