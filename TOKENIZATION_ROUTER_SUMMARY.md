# TokenizationCalldataRouter 路由实现总结

## 概述
成功实现了 `TokenizationCalldataRouter` 路由管理器，参考 `deploy.py` 的路由逻辑，为各种 tokenization 操作提供统一的 calldata/instructions 构建接口，根据代币标准自动选择 ERC20 或 Token 2022 的实现。

## 🆕 新增功能：Token 2022 访问控制

### 访问控制逻辑
在 Token 2022 的 mint 操作中，新增了基于 `token_access_activated` 标志的自动访问控制：

- **当 `token_access_activated = True`**: 新铸造的代币保持 unfrozen 状态（可转移）
- **当 `token_access_activated = False`**: 新铸造的代币自动冻结（不可转移）

### 实现细节
```python
# 在 Token2022InstructionManager.build_mint_instructions() 中
token_access_activated = cls._get_token_access_activation(token)

if not token_access_activated:
    # 铸造后自动冻结账户
    freeze_ix = program.instruction["freezeAccount"](...)
    instructions.append(freeze_ix)
```

### 配置获取
```python
@classmethod
def _get_token_access_activation(cls, token: Token) -> bool:
    """从 token.extra 中获取访问控制状态"""
    try:
        extra = TokenizationExtra.model_validate(token.extra)
        return extra.token_access_activated
    except Exception:
        return False  # 默认为未激活
```

## 核心路由器

### TokenizationCalldataRouter
位置：`waas2/tokenization/managers/calldata/router.py`

#### 路由逻辑
```python
if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
    return CoboERC20CalldataManager.build_xxx_calldata(...)
elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
    return Token2022InstructionManager.build_xxx_instructions(...)
else:
    raise ValueError(f"Unsupported token standard: {token.standard}")
```

## 路由方法

### 1. Mint (铸造)
```python
@classmethod
def build_mint_calldata(
    cls, token: Token, mints: List, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 返回 calldata 字符串
- **Token 2022**: 返回 instruction 列表

### 2. Burn (销毁)
```python
@classmethod
def build_burn_calldata(
    cls, token: Token, burns: List[...], execute_address: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `burn`/`burnFrom` 函数
- **Token 2022**: 使用 `burn` 指令

### 3. Allowlist (白名单)
```python
@classmethod
def build_allowlist_calldata(
    cls, token: Token, addresses: List[str], operation: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `accessListAdd`/`accessListRemove` 函数
- **Token 2022**: 使用 `thawAccount` 指令（解冻实现白名单）

### 4. Blocklist (黑名单)
```python
@classmethod
def build_blocklist_calldata(
    cls, token: Token, addresses: List[str], operation: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `blockListAdd`/`blockListRemove` 函数
- **Token 2022**: 使用 `freezeAccount` 指令（冻结实现黑名单）

### 5. Toggle Allowlist (白名单激活)
```python
@classmethod
def build_toggle_allowlist_calldata(
    cls, token: Token, enabled: bool, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `toggleAccesslist` 函数
- **Token 2022**: 暂不支持，返回空列表

### 6. Pause (暂停)
```python
@classmethod
def build_pause_calldata(
    cls, token: Token, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `pause` 函数
- **Token 2022**: 使用 `pause` 指令

### 7. Unpause (恢复)
```python
@classmethod
def build_unpause_calldata(
    cls, token: Token, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `unpause` 函数
- **Token 2022**: 使用 `resume` 指令

## 管理器迁移

### 已迁移的管理器
1. **TokenizationMintManager** (`mint.py`)
2. **TokenizationBurnManager** (`burn.py`)
3. **TokenizationPauseManager** (`pause.py`)
4. **TokenizationUnpauseManager** (`unpause.py`)
5. **TokenizationAllowlistManager** (`allowlist.py`)
6. **TokenizationBlocklistManager** (`blocklist.py`)

### 迁移内容
- ✅ 添加路由器导入
- ✅ 替换直接的 calldata 构建调用为路由器调用
- ✅ 移除旧的 `_build_xxx_calldata` 方法
- ✅ 简化参数传递

### 迁移示例
**之前**:
```python
calldata = cls._build_mint_calldata(token, params.mints)
```

**之后**:
```python
calldata = TokenizationCalldataRouter.build_mint_calldata(token, params.mints)
```

## 技术特点

### 1. 统一接口
- 所有管理器使用相同的路由器接口
- 隐藏底层实现差异
- 支持未来扩展新的代币标准

### 2. 类型安全
- 使用 `Union[str, List[SolContractCallInstruction]]` 返回类型
- 明确区分 EVM calldata 和 Solana instructions

### 3. 参数优化
- 可选的 `chain_id` 参数，默认使用 `token.chain_id`
- 简化的方法签名

### 4. 错误处理
- 对不支持的代币标准抛出明确错误
- 对不支持的功能给出警告日志

## Solana vs EVM 映射

| 功能 | EVM 实现 | Solana 实现 | 说明 |
|------|----------|-------------|------|
| Mint | `mint()` | `mintTo` | 直接对应 |
| Burn | `burn()`/`burnFrom()` | `burn` | 直接对应 |
| Allowlist | `accessListAdd/Remove` | `thawAccount` | 解冻=白名单 |
| Blocklist | `blockListAdd/Remove` | `freezeAccount` | 冻结=黑名单 |
| Toggle Allowlist | `toggleAccesslist` | 不支持 | 返回空列表 |
| Pause | `pause()` | `pause` | 直接对应 |
| Unpause | `unpause()` | `resume` | 直接对应 |

## 验证结果
✅ 所有语法检查通过
✅ 路由方法完整实现
✅ 各管理器成功迁移
✅ 旧代码完全清理
✅ 导入关系正确

## 使用方式
现在各种 tokenization 操作都通过统一的路由器进行，开发者无需关心底层是 ERC20 还是 Token 2022，路由器会根据 `token.standard` 自动选择正确的实现。

## 🧪 全面测试套件

### 测试文件结构
```
waas2/tokenization/tests/
├── test_calldata_router.py           # 路由器单元测试
├── test_token_2022_access_control.py # Token 2022 访问控制测试
├── test_tokenization_integration.py  # 集成测试
├── test_tokenization_edge_cases.py   # 边界条件和错误处理测试
└── test_tokenization_performance.py  # 性能测试
```

### 测试覆盖范围

#### 1. 单元测试 (`test_calldata_router.py`)
- ✅ ERC20 和 Token 2022 路由逻辑
- ✅ 所有操作类型的路由测试
- ✅ 不支持操作的错误处理
- ✅ 参数传递和覆盖测试

#### 2. 访问控制测试 (`test_token_2022_access_control.py`)
- ✅ `token_access_activated = True` 时的行为
- ✅ `token_access_activated = False` 时的自动冻结
- ✅ 批量操作的访问控制
- ✅ 无效配置的错误处理
- ✅ 缺少权限的异常处理

#### 3. 集成测试 (`test_tokenization_integration.py`)
- ✅ 完整工作流程测试（mint → pause → unpause → burn）
- ✅ 访问控制工作流程测试
- ✅ 跨链兼容性测试
- ✅ 批量操作性能测试

#### 4. 边界条件测试 (`test_tokenization_edge_cases.py`)
- ✅ 不支持的代币标准
- ✅ 空参数列表
- ✅ 无效地址格式
- ✅ 零金额和负金额
- ✅ 非常大的金额
- ✅ 并发操作模拟
- ✅ 内存使用测试

#### 5. 性能测试 (`test_tokenization_performance.py`)
- ✅ 小、中、大批量操作性能
- ✅ ERC20 vs Token 2022 性能对比
- ✅ 内存使用扩展性测试
- ✅ 并发性能测试
- ✅ 重复操作稳定性测试

### 测试执行
所有测试文件语法正确，可通过以下命令运行：
```bash
python -m pytest waas2/tokenization/tests/test_calldata_router.py -v
python -m pytest waas2/tokenization/tests/test_token_2022_access_control.py -v
python -m pytest waas2/tokenization/tests/test_tokenization_integration.py -v
python -m pytest waas2/tokenization/tests/test_tokenization_edge_cases.py -v
python -m pytest waas2/tokenization/tests/test_tokenization_performance.py -v
```

## 优势
1. **代码复用**: 消除了重复的 calldata 构建逻辑
2. **易于维护**: 集中管理所有 calldata 构建逻辑
3. **扩展性强**: 易于添加新的代币标准支持
4. **类型安全**: 明确的返回类型定义
5. **一致性**: 统一的接口设计
6. **🆕 智能访问控制**: 自动处理 Token 2022 的冻结/解冻逻辑
7. **🆕 全面测试**: 覆盖单元、集成、性能和边界条件测试
