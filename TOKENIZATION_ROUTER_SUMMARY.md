# TokenizationCalldataRouter 路由实现总结

## 概述
成功实现了 `TokenizationCalldataRouter` 路由管理器，参考 `deploy.py` 的路由逻辑，为各种 tokenization 操作提供统一的 calldata/instructions 构建接口，根据代币标准自动选择 ERC20 或 Token 2022 的实现。

## 核心路由器

### TokenizationCalldataRouter
位置：`waas2/tokenization/managers/calldata/router.py`

#### 路由逻辑
```python
if token.standard == cobo_waas2.TokenizationTokenStandard.ERC20:
    return CoboERC20CalldataManager.build_xxx_calldata(...)
elif token.standard == cobo_waas2.TokenizationTokenStandard.SPLTOKEN2022:
    return Token2022InstructionManager.build_xxx_instructions(...)
else:
    raise ValueError(f"Unsupported token standard: {token.standard}")
```

## 路由方法

### 1. Mint (铸造)
```python
@classmethod
def build_mint_calldata(
    cls, token: Token, mints: List, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 返回 calldata 字符串
- **Token 2022**: 返回 instruction 列表

### 2. Burn (销毁)
```python
@classmethod
def build_burn_calldata(
    cls, token: Token, burns: List[...], execute_address: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `burn`/`burnFrom` 函数
- **Token 2022**: 使用 `burn` 指令

### 3. Allowlist (白名单)
```python
@classmethod
def build_allowlist_calldata(
    cls, token: Token, addresses: List[str], operation: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `accessListAdd`/`accessListRemove` 函数
- **Token 2022**: 使用 `thawAccount` 指令（解冻实现白名单）

### 4. Blocklist (黑名单)
```python
@classmethod
def build_blocklist_calldata(
    cls, token: Token, addresses: List[str], operation: str, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `blockListAdd`/`blockListRemove` 函数
- **Token 2022**: 使用 `freezeAccount` 指令（冻结实现黑名单）

### 5. Toggle Allowlist (白名单激活)
```python
@classmethod
def build_toggle_allowlist_calldata(
    cls, token: Token, enabled: bool, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `toggleAccesslist` 函数
- **Token 2022**: 暂不支持，返回空列表

### 6. Pause (暂停)
```python
@classmethod
def build_pause_calldata(
    cls, token: Token, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `pause` 函数
- **Token 2022**: 使用 `pause` 指令

### 7. Unpause (恢复)
```python
@classmethod
def build_unpause_calldata(
    cls, token: Token, chain_id: str = None
) -> Union[str, List[SolContractCallInstruction]]:
```
- **ERC20**: 使用 `unpause` 函数
- **Token 2022**: 使用 `resume` 指令

## 管理器迁移

### 已迁移的管理器
1. **TokenizationMintManager** (`mint.py`)
2. **TokenizationBurnManager** (`burn.py`)
3. **TokenizationPauseManager** (`pause.py`)
4. **TokenizationUnpauseManager** (`unpause.py`)
5. **TokenizationAllowlistManager** (`allowlist.py`)
6. **TokenizationBlocklistManager** (`blocklist.py`)

### 迁移内容
- ✅ 添加路由器导入
- ✅ 替换直接的 calldata 构建调用为路由器调用
- ✅ 移除旧的 `_build_xxx_calldata` 方法
- ✅ 简化参数传递

### 迁移示例
**之前**:
```python
calldata = cls._build_mint_calldata(token, params.mints)
```

**之后**:
```python
calldata = TokenizationCalldataRouter.build_mint_calldata(token, params.mints)
```

## 技术特点

### 1. 统一接口
- 所有管理器使用相同的路由器接口
- 隐藏底层实现差异
- 支持未来扩展新的代币标准

### 2. 类型安全
- 使用 `Union[str, List[SolContractCallInstruction]]` 返回类型
- 明确区分 EVM calldata 和 Solana instructions

### 3. 参数优化
- 可选的 `chain_id` 参数，默认使用 `token.chain_id`
- 简化的方法签名

### 4. 错误处理
- 对不支持的代币标准抛出明确错误
- 对不支持的功能给出警告日志

## Solana vs EVM 映射

| 功能 | EVM 实现 | Solana 实现 | 说明 |
|------|----------|-------------|------|
| Mint | `mint()` | `mintTo` | 直接对应 |
| Burn | `burn()`/`burnFrom()` | `burn` | 直接对应 |
| Allowlist | `accessListAdd/Remove` | `thawAccount` | 解冻=白名单 |
| Blocklist | `blockListAdd/Remove` | `freezeAccount` | 冻结=黑名单 |
| Toggle Allowlist | `toggleAccesslist` | 不支持 | 返回空列表 |
| Pause | `pause()` | `pause` | 直接对应 |
| Unpause | `unpause()` | `resume` | 直接对应 |

## 验证结果
✅ 所有语法检查通过
✅ 路由方法完整实现
✅ 各管理器成功迁移
✅ 旧代码完全清理
✅ 导入关系正确

## 使用方式
现在各种 tokenization 操作都通过统一的路由器进行，开发者无需关心底层是 ERC20 还是 Token 2022，路由器会根据 `token.standard` 自动选择正确的实现。

## 优势
1. **代码复用**: 消除了重复的 calldata 构建逻辑
2. **易于维护**: 集中管理所有 calldata 构建逻辑
3. **扩展性强**: 易于添加新的代币标准支持
4. **类型安全**: 明确的返回类型定义
5. **一致性**: 统一的接口设计
